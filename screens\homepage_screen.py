from kivymd.app import MDApp
from kivy.metrics import dp
from kivy.properties import StringProperty, ObjectProperty, BooleanProperty
from screens.base_screen import BaseScreen
from kivy.clock import Clock
from kivy.lang import Builder
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.widget import Widget
import os
import json
import sys
from datetime import datetime
import threading
import tempfile
import traceback
from kivy.logger import Logger
from kivy.uix.progressbar import ProgressBar

from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.button import MDIconButton, MDButtonText, MDButton
from kivymd.uix.label import MDLabel
from kivymd.uix.card import MDCard
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.dialog import MDDialog
from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
from kivymd.uix.list import MDList, MDListItem

# 导入主题和字体样式
from theme import AppTheme, AppMetrics, FontStyles, FontManager

# 导入Logo组件
from widgets.logo import HealthLogo, add_logo_to_layout

# 定义KV语言字符串
KV = '''
<HealthFeatureCard>:
    orientation: 'vertical'
    size_hint: None, None
    size: dp(95), dp(95)  # 稍微减小卡片
    md_bg_color: app.theme.CARD_BACKGROUND
    radius: [dp(12)]  # 更圆润的边角
    elevation: 2  # 增加阴影效果
    padding: [dp(5), dp(10), dp(5), dp(5)]
    pos_hint: {'center_x': 0.5}

    MDIconButton:
        icon: root.icon
        icon_size: dp(36)  # 更大的图标
        pos_hint: {'center_x': 0.5}
        theme_icon_color: "Custom"
        icon_color: app.theme.PRIMARY_COLOR

    MDLabel:
        text: root.title
        halign: 'center'
        font_style: "Body"
        role: "small"
        theme_text_color: "Custom"
        text_color: app.theme.TEXT_PRIMARY
        size_hint_y: None
        height: self.texture_size[1]
        padding: [0, dp(4), 0, 0]

<CategoryLabel>:
    text: root.title
    halign: 'left'
    font_style: "Body"
    theme_text_color: "Custom"
    text_color: app.theme.TEXT_PRIMARY
    bold: True
    size_hint_y: None
    height: self.texture_size[1]
    padding: [0, dp(8), 0, dp(8)]

<ViewMoreButton>:
    style: "text"
    pos_hint: {"right": 1}

    MDButtonText:
        text: "查看更多"
        theme_text_color: "Primary"
        font_style: "Body"
        role: "small"

<NavBarButton>:
    orientation: 'vertical'
    adaptive_height: True
    spacing: dp(1)  # 减小图标与文字的间距
    padding: [dp(2), dp(2), dp(2), dp(2)]
    pos_hint: {'center_x': 0.5}

    MDIconButton:
        icon: root.icon
        icon_size: dp(22)  # 稍微减小图标
        pos_hint: {'center_x': 0.5}
        theme_icon_color: "Custom"
        icon_color: root.selected and app.theme.PRIMARY_COLOR or app.theme.TEXT_SECONDARY
        on_release: root.on_press()

    MDLabel:
        text: root.text
        halign: 'center'
        font_style: "Body"
        role: "small"
        font_size: dp(10)  # 指定较小的字体大小
        theme_text_color: "Custom"
        text_color: root.selected and app.theme.PRIMARY_COLOR or app.theme.TEXT_SECONDARY
        size_hint_y: None
        height: self.texture_size[1]
        shorten: True  # 如果文本太长会缩短显示
        shorten_from: 'right'

<HomepageScreen>:
    canvas.before:
        Color:
            rgba: app.theme.PRIMARY_LIGHT
        Rectangle:
            pos: self.pos
            size: self.size

    MDBoxLayout:
        orientation: 'vertical'

        # 滚动内容区域
        MDScrollView:
            id: scroll_view
            do_scroll_x: False
            do_scroll_y: True

            # 主内容容器
            MDBoxLayout:
                id: main_layout
                orientation: 'vertical'
                size_hint_y: None
                height: self.minimum_height
                padding: [dp(12), dp(0), dp(12), dp(16)]  # 减少顶部间距
                spacing: dp(5)  # 减少组件间隔

                # Logo区域
                MDBoxLayout:
                    id: logo_container_wrap
                    orientation: 'vertical'
                    size_hint_y: None
                    height: dp(220)  # 增加高度确保显示完整
                    padding: [0, dp(10), 0, dp(5)]

                    # Logo主容器
                    MDBoxLayout:
                        id: logo_container
                        orientation: 'vertical'
                        size_hint_y: None
                        height: dp(200)  # Logo区域高度

                        # 使用统一的HealthLogo组件
                        HealthLogo:
                            id: health_logo

                # 欢迎消息框 - 无背景色
                MDBoxLayout:
                    orientation: 'horizontal'
                    size_hint_y: None
                    height: dp(30)
                    padding: [dp(5), dp(0), dp(5), dp(0)]
                    spacing: dp(0)

                    # 欢迎词
                    MDLabel:
                        id: welcome_label
                        text: "欢迎 XXX 先生/女士"
                        halign: 'left'
                        font_style: "Body"
                        role: "medium"
                        bold: True
                        theme_text_color: "Custom"
                        text_color: app.theme.TEXT_PRIMARY
                        size_hint_x: 0.7

                    # 退出按钮
                    MDButton:
                        style: "text"
                        size_hint_x: 0.3
                        pos_hint: {"center_y": 0.5, "right": 1}
                        on_release: root.on_logout()

                        MDButtonText:
                            text: "退出"
                            font_style: "Body"
                            role: "medium"
                            bold: True
                            theme_text_color: "Custom"
                            text_color: app.theme.PRIMARY_COLOR

                # 健康资料收集区域
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(15)
                    padding: [dp(5), dp(25), dp(5), dp(15)]

                    MDBoxLayout:
                        size_hint_y: None
                        height: dp(40)

                        CategoryLabel:
                            title: "健康资料收集"
                            size_hint_x: 0.7
                            pos_hint: {"center_y": 0.5}
                            bold: True
                            font_size: app.metrics.FONT_SIZE_MEDIUM

                        ViewMoreButton:
                            size_hint_x: 0.3
                            pos_hint: {"center_y": 0.5}
                            on_release: root.on_view_more("health_data")

                    # 收集功能按钮网格
                    MDGridLayout:
                        id: collection_grid
                        cols: 3
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(15)

                # 健康资料查询区域
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(15)
                    padding: [dp(5), dp(25), dp(5), dp(15)]

                    MDBoxLayout:
                        size_hint_y: None
                        height: dp(40)

                        CategoryLabel:
                            title: "健康资料查询"
                            size_hint_x: 0.7
                            pos_hint: {"center_y": 0.5}
                            bold: True
                            font_size: app.metrics.FONT_SIZE_MEDIUM

                        ViewMoreButton:
                            size_hint_x: 0.3
                            pos_hint: {"center_y": 0.5}
                            on_release: root.on_view_more("health_query")

                    # 查询功能按钮网格
                    MDGridLayout:
                        id: query_grid
                        cols: 3
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(15)

                # 健康分析区域
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(15)
                    padding: [dp(5), dp(25), dp(5), dp(15)]

                    MDBoxLayout:
                        size_hint_y: None
                        height: dp(40)

                        CategoryLabel:
                            title: "健康分析"
                            size_hint_x: 0.7
                            pos_hint: {"center_y": 0.5}
                            bold: True
                            font_size: app.metrics.FONT_SIZE_MEDIUM

                        ViewMoreButton:
                            size_hint_x: 0.3
                            pos_hint: {"center_y": 0.5}
                            on_release: root.on_view_more("health_analysis")

                    # 分析功能按钮网格 - 改为水平排列
                    MDGridLayout:
                        id: analysis_grid
                        cols: 3  # 改为3列水平排列
                        size_hint_y: None
                        height: self.minimum_height
                        spacing: dp(15)

                # 语音分诊区域
                MDBoxLayout:
                    orientation: 'vertical'
                    size_hint_y: None
                    height: self.minimum_height
                    spacing: dp(15)
                    padding: [dp(5), dp(25), dp(5), dp(15)]

                    MDBoxLayout:
                        size_hint_y: None
                        height: dp(40)

                        CategoryLabel:
                            title: "语音分诊"
                            size_hint_x: 0.7
                            pos_hint: {"center_y": 0.5}
                            bold: True
                            font_size: app.metrics.FONT_SIZE_MEDIUM

                        # 为保持一致性添加一个空白占位
                        Widget:
                            size_hint_x: 0.3

                    # 语音按钮卡片
                    MDCard:
                        size_hint_y: None
                        height: dp(80)
                        md_bg_color: app.theme.CARD_BACKGROUND
                        radius: [dp(12)]  # 与其他卡片保持一致
                        elevation: 2  # 增加阴影效果
                        padding: [dp(16), dp(8), dp(16), dp(8)]
                        on_release: root.navigate_to_voice_triage()

                        MDBoxLayout:
                            orientation: 'horizontal'

                            MDIconButton:
                                icon: "microphone"
                                icon_size: dp(36)  # 更大的图标
                                theme_icon_color: "Custom"
                                icon_color: app.theme.PRIMARY_COLOR

                            MDLabel:
                                text: "点击开始语音聊天"
                                font_style: "Body"
                                role: "medium"
                                bold: True  # 加粗显示
                                theme_text_color: "Custom"
                                text_color: app.theme.TEXT_PRIMARY
                                padding: [dp(16), 0, dp(16), 0]  # 替换padding_x

        # 底部导航栏
        MDCard:
            id: navbar
            size_hint_y: None
            height: dp(65)  # 略微增加高度
            elevation: 4
            md_bg_color: app.theme.CARD_BACKGROUND
            radius: [dp(0)]

            MDBoxLayout:
                id: navbar_content
                orientation: 'horizontal'
                size_hint_x: 1
                pos_hint: {'center_x': 0.5}
                padding: [dp(5), dp(4), dp(5), dp(4)]
                spacing: dp(0)  # 按钮之间完全无间距，依靠按钮自身来控制间距
'''

# 加载KV语言字符串
Builder.load_string(KV)

class HealthFeatureCard(MDCard):
    """健康功能卡片"""
    icon = StringProperty("heart-pulse")
    title = StringProperty("功能")

    def __init__(self, **kwargs):
        super(HealthFeatureCard, self).__init__(**kwargs)

class CategoryLabel(MDLabel):
    """分类标签"""
    title = StringProperty("分类")

    def __init__(self, **kwargs):
        super(CategoryLabel, self).__init__(**kwargs)

class ViewMoreButton(MDButton):
    """查看更多按钮"""
    def __init__(self, **kwargs):
        super(ViewMoreButton, self).__init__(**kwargs)

class NavBarButton(MDBoxLayout):
    """导航栏按钮"""
    icon = StringProperty("home")
    text = StringProperty("首页")
    selected = BooleanProperty(False)

    def __init__(self, **kwargs):
        self.callback = kwargs.pop('callback', None)
        # 设置默认的size_hint_x如果没有提供
        if 'size_hint_x' not in kwargs:
            kwargs['size_hint_x'] = None
        super(NavBarButton, self).__init__(**kwargs)

    def on_press(self):
        if self.callback:
            self.callback(self)

class HomepageScreen(BaseScreen):
    """首页屏幕"""
    user_name = StringProperty("XXX")
    user_gender = StringProperty("先生")  # 默认为先生，可以是"先生"或"女士"

    def init_ui(self, dt=0):
        """初始化UI组件"""
        # Logo is already defined in the kv template, don't add it again

        # 设置欢迎信息
        self.set_welcome_message()

        # 添加健康资料收集功能
        self.add_collection_features()

        # 添加健康资料查询功能
        self.add_query_features()

        # 添加健康分析功能
        self.add_analysis_features()

        # 添加底部导航栏
        self.add_navigation_bar()

    def on_enter(self):
        """进入页面时的处理"""
        # 获取当前用户数据，并设置欢迎消息
        self.load_user_data()

    def load_user_data(self):
        """加载当前用户数据并更新UI"""
        try:
            # 获取用户管理器和当前用户信息
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            # 获取应用实例，以便访问app.user_data
            app = MDApp.get_running_app()

            if current_user:
                # 从当前用户获取姓名和性别
                # 使用标准字段名full_name
                name = current_user.full_name
                if not name or name.strip() == "":
                    name = current_user.username

                # 打印详细的用户信息，帮助调试
                print(f"主页显示用户信息: full_name={current_user.full_name}, username={current_user.username}")

                # 检查是否有custom_id字段
                user_id_display = None
                if hasattr(current_user, 'custom_id') and current_user.custom_id:
                    user_id_display = current_user.custom_id
                    print(f"用户custom_id: {current_user.custom_id}")
                elif hasattr(current_user, 'user_id') and current_user.user_id:
                    user_id_display = current_user.user_id
                    print(f"用户user_id: {current_user.user_id}")
                else:
                    # 如果没有任何ID，记录错误
                    print("错误: 用户没有custom_id或user_id")
                    Logger.error("HomepageScreen: No user ID available")

                # 确保app.user_data中有user_id字段，用于其他功能使用
                app = MDApp.get_running_app()
                if hasattr(app, 'user_data') and isinstance(app.user_data, dict):
                    app.user_data['user_id'] = user_id_display
                    app.user_data['custom_id'] = user_id_display
                    print(f"已将user_id和custom_id设置为: {user_id_display}")

                print(f"主页最终显示: 姓名={name}, 用户ID={user_id_display}")

                # 从性别字段获取性别，如果不存在则根据常用名字推断
                gender = current_user.gender
                if not gender:
                    # 简单判断：如果名字最后一个字是"娜"、"兰"等常见女性字，设置为"女士"，否则默认为"先生"
                    female_endings = ["娜", "兰", "妮", "芳", "英", "娜", "丽", "萍", "敏", "婷", "玲", "娟", "静", "梅", "燕", "红", "琼", "芬"]
                    if name and any(name.endswith(ending) for ending in female_endings):
                        gender = "女"
                    else:
                        gender = "男"

                # 设置性别显示形式
                gender_display = "女士" if gender == "女" else "先生"

                # 设置欢迎消息
                self.set_welcome_message(name, gender_display)

                # 打印调试信息
                if hasattr(current_user, 'custom_id') and current_user.custom_id:
                    print(f"从user_manager获取到用户信息: 姓名={name}, ID={current_user.custom_id}")
                else:
                    print(f"从user_manager获取到用户信息: 姓名={name}, ID=None")
                    print(f"警告: 用户没有custom_id，这可能导致某些功能无法正常工作")

                    # 尝试从CloudAPI获取custom_id
                    try:
                        from utils.cloud_api import get_cloud_api
                        cloud_api = get_cloud_api()
                        if cloud_api.is_authenticated():
                            user_info = cloud_api.get_user_info()
                            if user_info and "custom_id" in user_info:
                                print(f"从CloudAPI获取到custom_id: {user_info['custom_id']}")
                                # 更新用户的custom_id
                                current_user.custom_id = user_info["custom_id"]
                                # 更新app.user_data中的custom_id
                                app = MDApp.get_running_app()
                                if hasattr(app, 'user_data') and isinstance(app.user_data, dict):
                                    app.user_data['custom_id'] = current_user.custom_id
                                    app.user_data['user_id'] = current_user.custom_id  # 兼容旧代码
                                # 保存更新后的用户信息
                                user_manager.save_current_user(force_save=True)
                                print(f"已更新用户的custom_id: {current_user.custom_id}")
                    except Exception as e:
                        print(f"尝试从CloudAPI获取custom_id时出错: {str(e)}")
            elif hasattr(app, 'user_data') and app.user_data:
                # 如果用户管理器中没有用户信息，尝试从app.user_data获取
                user_data = app.user_data

                # 获取真实姓名，使用标准字段名full_name
                name = user_data.get('full_name')
                if not name or name.strip() == "":
                    name = user_data.get('username', '')

                # 获取用户ID (优先使用custom_id)
                user_id_display = user_data.get('custom_id')
                if not user_id_display:
                    user_id_display = user_data.get('user_id', '')

                print(f"从app.user_data获取: 姓名={name}, 用户ID={user_id_display}")

                gender = user_data.get('gender', '男')

                # 设置性别显示形式
                gender_display = "女士" if gender == "女" else "先生"

                # 设置欢迎消息
                if name:
                    self.set_welcome_message(name, gender_display)
                    print(f"从app.user_data获取到用户信息: 姓名={name}")
                else:
                    # 如果app.user_data中也没有用户信息，显示默认欢迎消息
                    self.set_welcome_message("访客", "先生/女士")
                    print("app.user_data中未找到用户名")
            else:
                # 如果没有用户信息，显示默认欢迎消息
                self.set_welcome_message("访客", "先生/女士")
                print("未找到当前用户，需要登录")
        except Exception as e:
            # 如果出错，显示默认欢迎消息
            self.set_welcome_message("访客", "先生/女士")
            print(f"加载用户数据错误: {str(e)}")

    def set_welcome_message(self, name=None, gender=None):
        """设置欢迎消息

        Args:
            name: 用户真实姓名
            gender: 用户性别显示，如"先生"或"女士"
        """
        if not name:
            # 如果没有提供名字，使用实例属性中的名字
            name = self.user_name
        else:
            # 更新实例属性
            self.user_name = name

        # 打印调试信息
        print(f"设置欢迎消息: 姓名={name}, 性别={gender}")

        if not gender:
            # 如果没有提供性别显示，使用实例属性中的性别
            gender = self.user_gender
        else:
            # 更新实例属性
            self.user_gender = gender

        # 设置欢迎消息 - 确保显示"欢迎 真实姓名 先生/女士"的格式
        welcome_message = f"欢迎 {name} {gender}"
        if hasattr(self, 'ids') and hasattr(self.ids, 'welcome_label'):
            self.ids.welcome_label.text = welcome_message
            print(f"已更新欢迎消息: {welcome_message}")

    def add_collection_features(self):
        """添加健康资料收集功能卡片"""
        collection_grid = self.ids.collection_grid
        collection_grid.clear_widgets()

        # 健康基础信息
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="heart-pulse",
                title="健康基本信息",
                on_release=lambda x: self.navigate_to_health_info()
            )
        )

        # 文件上传
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="file-upload-outline",
                title="文件上传",
                on_release=lambda x: self.open_file_upload()
            )
        )

        # 二维码上传
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="qrcode",
                title="二维码上传",
                on_release=lambda x: self.open_qr_scanner()
            )
        )

        # 拍照上传
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="camera",
                title="拍照上传",
                on_release=lambda x: self.open_camera()
            )
        )

        #  调查问卷
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="form-select",
                title="调查问卷",
                on_release=lambda x: self.navigate_to_assessment_scales()
            )
        )

        # 评估量表
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="chart-line",
                title="评估量表",
                on_release=lambda x: self.navigate_to_assessment_scales()
            )
        )

        # 健康日志
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="notebook",
                title="健康日记"
            )
        )

        # 复诊日志
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="calendar",
                title="管理日志"
            )
        )

        # 药物记录
        collection_grid.add_widget(
            HealthFeatureCard(
                icon="pill",
                title="药物记录"
            )
        )

    def add_query_features(self):
        """添加健康资料查询功能卡片"""
        query_grid = self.ids.query_grid
        query_grid.clear_widgets()

        # 健康状况一览
        query_grid.add_widget(
            HealthFeatureCard(
                icon="heart-pulse",
                title="健康状况一览表",
                on_release=lambda x: self.navigate_to_general_health()
            )
        )

        # 文档列表
        query_grid.add_widget(
            HealthFeatureCard(
                icon="file-document-outline",
                title="文档列表",
                on_release=lambda x: self.navigate_to_document_list()
            )
        )

        # 住院病历
        query_grid.add_widget(
            HealthFeatureCard(
                icon="file-document",
                title="住院病历"
            )
        )

        # 门诊病历
        query_grid.add_widget(
            HealthFeatureCard(
                icon="hospital-building",
                title="门诊病历"
            )
        )

        # 化验报告
        query_grid.add_widget(
            HealthFeatureCard(
                icon="test-tube",
                title="化验报告"
            )
        )

        # 检验报告
        query_grid.add_widget(
            HealthFeatureCard(
                icon="clipboard-text",
                title="检查报告"
            )
        )

        # 健康报告
        query_grid.add_widget(
            HealthFeatureCard(
                icon="file-document-check",
                title="体检报告"
            )
        )

    def add_analysis_features(self):
        """添加健康分析功能卡片"""
        analysis_grid = self.ids.analysis_grid
        analysis_grid.clear_widgets()

        # 慢性疾病风险预测
        analysis_grid.add_widget(
            HealthFeatureCard(
                icon="chart-line-variant",
                title="慢性疾病风险预测"
            )
        )

        # 恶性肿瘤风险预测
        analysis_grid.add_widget(
            HealthFeatureCard(
                icon="dna",
                title="恶性肿瘤风险预测"
            )
        )

        # 个性化体检方案
        analysis_grid.add_widget(
            HealthFeatureCard(
                icon="account-heart",
                title="个性化体检方案"
            )
        )

    def add_navigation_bar(self):
        """添加底部导航栏"""
        navbar = self.ids.navbar_content
        navbar.clear_widgets()

        # 导航按钮
        buttons = [
            {"icon": "home", "text": "主页", "selected": True},
            {"icon": "folder", "text": "资料管理", "selected": False},
            {"icon": "chart-bar", "text": "数据分析", "selected": False},
            {"icon": "chat-processing", "text": "语音分诊", "selected": False},
            {"icon": "account", "text": "我的", "selected": False}
        ]

        # 添加所有导航按钮，并设置它们的大小为等宽
        for btn_data in buttons:
            btn = NavBarButton(
                icon=btn_data["icon"],
                text=btn_data["text"],
                selected=btn_data["selected"],
                size_hint_x=0.2,  # 五个按钮平均分配空间
                callback=self.on_nav_button_press
            )
            navbar.add_widget(btn)

    def on_nav_button_press(self, button):
        """处理导航按钮点击"""
        # 取消所有按钮的选中状态
        navbar = self.ids.navbar_content
        for child in navbar.children:
            if isinstance(child, NavBarButton):
                child.selected = False

        # 设置当前按钮为选中状态
        button.selected = True

        # 处理导航逻辑
        if button.text == "主页":
            # 已经在主页，不需要操作
            pass
        elif button.text == "资料管理":
            # 切换到资料管理页面
            print("切换到资料管理页面")
        elif button.text == "数据分析":
            # 切换到数据分析页面
            print("切换到数据分析页面")
        elif button.text == "语音分诊":
            # 切换到语音分诊页面
            self.navigate_to_voice_triage()
        elif button.text == "我的":
            # 切换到个人页面
            self.manager.current = 'profile'

    def on_view_more(self, category):
        """查看更多功能"""
        print(f"查看更多: {category}")
        # 可以根据category实现不同类别的跳转

    def on_logout(self):
        """退出登录"""
        # 退出应用程序
        from kivy.core.window import Window
        app = MDApp.get_running_app()

        # 使用getattr安全地调用stop方法
        stop_method = getattr(app, 'stop', None)
        if callable(stop_method):
            stop_method()
        else:
            # 如果stop方法不可用，使用Window.close()
            Window.close()

    def navigate_to_health_info(self):
        """导航到健康基本信息页面"""
        try:
            # 获取当前应用实例和用户管理器
            app = MDApp.get_running_app()
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            # 如果当前有登录用户，确保app.user_data中有用户信息
            if current_user:
                # 获取完整的用户数据
                from utils.storage import UserStorage
                user_data = UserStorage.get_user_data()

                # 安全地设置用户数据
                set_user_data = getattr(app, 'set_user_data', None)
                if callable(set_user_data):
                    set_user_data(user_data)

                print(f"导航到健康基本信息页面: 用户ID={current_user.user_id}")
            else:
                print("警告: 未找到当前登录用户")

            # 切换到健康信息录入页面
            self.manager.current = 'primaryhealth_screen'
        except Exception as e:
            print(f"导航到健康基本信息页面失败: {e}")
            self.show_error("页面跳转失败，请稍后重试")

    def navigate_to_general_health(self):
        """导航到健康状况一览表页面"""
        try:
            self.manager.current = 'generalhealth_screen'
        except Exception as e:
            print(f"导航到健康状况一览表页面时出错: {str(e)}")

    def navigate_to_document_list(self):
        """导航到文档列表页面"""
        try:
            # 导入文档列表屏幕
            from screens.document_list_screen import DocumentListScreen

            # 检查是否已添加到屏幕管理器
            if not self.manager.has_screen('document_list_screen'):
                self.manager.add_widget(DocumentListScreen(name='document_list_screen'))

            # 切换到文档列表页面
            self.manager.current = 'document_list_screen'
            print("已切换到文档列表页面")
        except Exception as e:
            print(f"导航到文档列表页面时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            self.show_error("无法打开文档列表页面，请稍后重试")

    def navigate_to_voice_triage(self):
        """导航到语音分诊页面"""
        try:
            # 加载语音分诊屏幕
            from screens.voice_triage_screen import VoiceTriageScreen

            # 检查是否已添加到屏幕管理器
            if not self.manager.has_screen('voice_triage_screen'):
                self.manager.add_widget(VoiceTriageScreen(name='voice_triage_screen'))

            # 切换到语音分诊页面
            self.manager.current = 'voice_triage_screen'
            print("已切换到语音分诊页面")
        except Exception as e:
            print(f"导航到语音分诊页面时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            self.show_error("无法打开语音分诊页面，请稍后重试")

    def navigate_to_assessment_scales(self):
        """导航到评估量表页面"""
        try:
            # 检查是否已添加到屏幕管理器
            if not self.manager.has_screen('survey_screen'):
                from screens.survey_screen import SurveyScreen
                self.manager.add_widget(SurveyScreen(name='survey_screen'))

            # 切换到评估量表页面
            self.manager.current = 'survey_screen'
            print("已切换到评估量表页面")
        except Exception as e:
            print(f"导航到评估量表页面时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            self.show_error("无法打开评估量表页面，请稍后重试")

    # 文件上传相关方法
    def safe_dismiss_dialog(self, dialog_attr_name):
        """安全地关闭对话框

        Args:
            dialog_attr_name: 对话框属性名称
        """
        if hasattr(self, dialog_attr_name):
            dialog = getattr(self, dialog_attr_name)
            if dialog is not None and hasattr(dialog, 'dismiss'):
                dialog.dismiss()

    def open_file_upload(self):
        """打开文件上传选项"""
        # 直接调用本地文件选择器，不再使用对话框
        self.select_local_file()

    def select_local_file(self):
        """选择本地文件上传（直接打开文件选择器）"""
        try:
            # 使用try-except块包裹导入语句，以防导入失败
            try:
                from plyer import filechooser
            except ImportError:
                filechooser = None
                raise ImportError("无法导入plyer.filechooser模块")

            self.show_loading_dialog("正在打开文件选择器...")
            print("正在打开文件选择器...")

            # 增强的空值检查
            if filechooser is None:
                raise ValueError("filechooser模块不可用")

            if not hasattr(filechooser, 'open_file'):
                raise ValueError("filechooser模块缺少open_file方法")

            # 确保filechooser.open_file是可调用的
            if not callable(getattr(filechooser, 'open_file', None)):
                raise ValueError("filechooser.open_file不是可调用对象")

            # 调用文件选择器
            if filechooser and hasattr(filechooser, 'open_file') and callable(filechooser.open_file):
                filechooser.open_file(
                    on_selection=self.handle_file_selection,
                    filters=[["PDF文件", "*.pdf"], ["Word文档", "*.doc", "*.docx"],
                            ["Excel文件", "*.xls", "*.xlsx"], ["图片文件", "*.jpg", "*.jpeg", "*.png"]]
                )
            else:
                raise ValueError("文件选择器不可用或open_file方法不可调用")
        except Exception as e:
            print(f"打开文件选择器失败: {e}")
            import traceback
            traceback.print_exc()
            self.dismiss_loading_dialog()
            self.show_error(f"选择文件失败: {str(e)}")

    def select_wechat_file(self):
        """选择微信文件（暂不可用）"""
        self.show_error("微信文件选择功能暂不可用")

    def handle_file_selection(self, selected_files):
        """处理文件选择器返回的文件路径

        Args:
            selected_files: 选择的文件路径列表
        """
        # 确保关闭加载对话框
        self.safe_dismiss_dialog('loading_dialog')

        # 检查selected_files是否为空
        if not selected_files:
            print("未选择任何文件")
            self.show_error("未选择任何文件")
            return

        # 确保selected_files是列表
        if not isinstance(selected_files, list):
            selected_files = [selected_files]

        # 获取第一个文件路径
        selected_file = selected_files[0]
        print(f"选择的文件: {selected_file}")

        # 检查文件是否存在
        if not os.path.exists(selected_file):
            print(f"文件不存在: {selected_file}")
            self.show_error(f"文件不存在: {selected_file}")
            return

        # 保存选中的文件路径
        self.selected_file = selected_file

        # 显示加载对话框
        self.show_loading_dialog("正在处理文件...")

        # 启动后台线程处理文件
        thread = threading.Thread(target=lambda: self.process_file_in_background(selected_file))
        thread.daemon = True
        thread.start()

    def process_file_in_background(self, file_path):
        """在后台处理选择的文件"""
        try:
            # 初始化API客户端
            from api.api_client import APIClient
            from utils.cloud_api import get_cloud_api
            from utils.user_manager import get_user_manager

            # 获取云API和用户管理器
            cloud_api = get_cloud_api()
            user_manager = get_user_manager()

            # 获取用户custom_id
            custom_id = user_manager.get_current_user_custom_id()
            print(f"当前用户custom_id: {custom_id}")

            # 检查用户是否有custom_id
            if not custom_id:
                print("用户没有custom_id，无法上传文件")
                # 尝试加载认证信息
                cloud_api.load_auth_info()

                # 再次检查是否有custom_id
                if hasattr(cloud_api, 'custom_id') and cloud_api.custom_id:
                    custom_id = cloud_api.custom_id
                    print(f"从cloud_api获取到custom_id: {custom_id}")
                    # 更新用户的custom_id
                    if user_manager.current_user:
                        user_manager.current_user.custom_id = custom_id
                        user_manager.save_current_user(force_save=True)
                        print(f"已更新用户的custom_id: {custom_id}")
                else:
                    # 检查用户是否已登录
                    if not cloud_api.is_authenticated():
                        print("用户未登录且没有custom_id，无法上传文件")
                        # 将文件添加到上传队列
                        metadata = {
                            "document_type": "medical_report",
                            "description": "移动端上传的文档",
                            "source": "mobile"
                        }

                        # 如果有user_id，添加到元数据中
                        if user_manager.current_user and hasattr(user_manager.current_user, 'user_id'):
                            metadata["user_id"] = user_manager.current_user.user_id
                            print(f"使用user_id添加到元数据: {user_manager.current_user.user_id}")

                        queued = cloud_api.add_to_upload_queue(file_path, metadata)
                        queue_msg = "已加入上传队列，稍后将自动上传" if queued else "添加到上传队列失败"

                        # 在主线程中更新UI
                        Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                        Clock.schedule_once(lambda dt: self.show_info(f"用户未登录，文件{queue_msg}"), 0)
                        return

            # 使用云端OCR服务
            api_client = APIClient()
            print(f"初始化API客户端完成，准备处理文件: {file_path}")

            # 文件预处理
            try:
                # 导入图像处理模块
                import cv2
                import os

                # 检查文件类型
                _, file_extension = os.path.splitext(file_path)
                file_extension = file_extension.lower()

                # 图像预处理 - 对图像文件进行预处理
                image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
                if file_extension in image_formats:
                    print(f"对图像文件进行预处理: {file_path}")
                    # 读取图像
                    image = cv2.imread(file_path)
                    if image is not None:
                        # 调整大小，保持宽高比
                        max_dim = 2000
                        height, width = image.shape[:2]
                        if max(height, width) > max_dim:
                            scale = max_dim / max(height, width)
                            image = cv2.resize(image, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
                            print(f"已调整图像大小: {width}x{height} -> {int(width*scale)}x{int(height*scale)}")

                        # 增强对比度
                        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                        l, a, b = cv2.split(lab)
                        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
                        cl = clahe.apply(l)
                        enhanced_lab = cv2.merge((cl, a, b))
                        enhanced_image = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)

                        # 保存预处理后的图像
                        processed_path = file_path.replace('.', '_processed.')
                        cv2.imwrite(processed_path, enhanced_image)
                        print(f"已保存预处理后的图像: {processed_path}")

                        # 使用预处理后的图像路径
                        file_path = processed_path
            except ImportError:
                print("未安装OpenCV，跳过图像预处理")
            except Exception as e:
                print(f"图像预处理失败，使用原始图像: {str(e)}")

            # 使用APIClient处理文件
            if api_client and api_client.cloud_api:
                result = api_client.upload_document(file_path)
                print(f"文件处理完成，结果: {result}")
            else:
                result = {"success": False, "message": "API客户端初始化失败"}
                print("API客户端初始化失败")

            # 检查是否有认证错误
            if isinstance(result, dict) and not result.get('success', True):
                error = result.get('error', '') or result.get('message', '')
                if error and ('API密钥' in error or 'API密钥' in result.get('text', '') or result.get('code') in ['AuthFailure', 'MissingApiKey']):
                    # 在主线程中调用显示API密钥错误对话框
                    Clock.schedule_once(lambda dt: self.show_api_key_error_dialog(), 0)
                    return

            # 在主线程中更新UI
            Clock.schedule_once(lambda dt: self.handle_ocr_result(file_path, result), 0)
        except Exception as upload_error:
            print(f"处理文件异常: {upload_error}")
            import traceback
            traceback.print_exc()
            # 在主线程中显示错误
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
            Clock.schedule_once(lambda dt: self.show_error(f"处理文件失败: {str(upload_error)}"), 0)

    # 二维码扫描相关方法
    def open_qr_scanner(self):
        """打开二维码扫描器"""
        try:
            # 导入二维码扫描器
            from utils.qrcode_utils import QRCodeScanner

            # 初始化扫描器
            self.qr_scanner = QRCodeScanner(callback=self.handle_qr_scan_result)

            # 显示正在启动相机提示
            self.show_info("正在启动相机...")

            # 启动扫描
            result = self.qr_scanner.start_scanning()
            if not result:
                self.show_error("无法启动相机，请检查权限设置")
                return

            # 显示扫描提示
            self.show_info("请将二维码对准相机")
        except Exception as e:
            print(f"启动二维码扫描器失败: {e}")
            self.show_error("二维码扫描功能初始化失败")

    def handle_qr_scan_result(self, result):
        """处理二维码扫描结果"""
        # 停止扫描器
        if hasattr(self, 'qr_scanner') and self.qr_scanner:
            self.qr_scanner.stop_scanning()

        if not result:
            self.show_error("未能识别二维码")
            return

        # 显示加载对话框
        self.show_loading_dialog("正在处理二维码数据...")

        # 处理二维码数据（例如链接到的文件）
        try:
            # 如果二维码包含文件URL
            if 'file_url' in result:
                from api.api_client import APIClient
                import requests
                import tempfile
                import os

                # 下载文件
                file_url = result['file_url']
                try:
                    # 创建临时文件
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.tmp')
                    temp_file.close()

                    # 下载文件
                    response = requests.get(file_url, stream=True)
                    response.raise_for_status()

                    with open(temp_file.name, 'wb') as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            f.write(chunk)

                    # 处理下载的文件
                    api_client = APIClient()
                    result = api_client.upload_document(temp_file.name)

                    # 关闭加载对话框
                    self.dismiss_loading_dialog()

                    # 显示OCR结果
                    self.handle_ocr_result(temp_file.name, result)

                except Exception as e:
                    print(f"下载或处理文件失败: {e}")
                    self.dismiss_loading_dialog()
                    self.show_error(f"下载或处理文件失败: {str(e)}")

                    # 清理临时文件
                    if os.path.exists(temp_file.name):
                        os.unlink(temp_file.name)

            # 如果二维码包含文本内容
            elif 'text' in result:
                # 直接处理文本内容
                text = result['text']

                # 关闭加载对话框
                self.dismiss_loading_dialog()

                # 创建确认对话框
                self.qr_dialog = MDDialog()
                self.qr_dialog.title = "二维码内容"

                # 添加内容
                from kivymd.uix.label import MDLabel
                content = MDLabel(
                    text=text,
                    adaptive_height=True
                )
                self.qr_dialog.ids.container.add_widget(content)

                # 添加按钮
                ok_button = MDButton(
                    MDButtonText(text="确定"),
                    style="text",
                    on_release=lambda x: self.qr_dialog.dismiss()
                )
                self.qr_dialog.ids.button_container.add_widget(ok_button)

                # 显示对话框
                self.qr_dialog.open()

            else:
                # 未识别的二维码格式
                self.dismiss_loading_dialog()
                self.show_error("无法识别二维码内容格式")

        except Exception as e:
            print(f"处理二维码数据失败: {e}")
            self.dismiss_loading_dialog()
            self.show_error(f"处理二维码数据失败: {str(e)}")

    # 相机拍照相关方法
    def open_camera(self):
        """打开相机拍照"""
        try:
            # 确保导入相机视图
            try:
                from widgets.camera_view import CameraView
                print("成功导入CameraView模块")
            except ImportError as e:
                print(f"导入CameraView模块失败: {e}")
                self.show_error("无法导入相机模块，请确保依赖库已安装")
                return

            # 显示正在启动相机提示
            self.show_info("正在启动相机...")

            # 初始化相机视图，使用document模式
            self.camera_view = CameraView(
                callback=self.handle_camera_capture,
                on_dismiss=lambda: print("相机视图已关闭"),
                mode="document"
            )

            # 检查相机视图是否成功创建
            if not hasattr(self, 'camera_view') or not self.camera_view:
                self.show_error("相机初始化失败")
                return

            print("相机视图已创建，准备打开")

            # 打开相机视图
            self.camera_view.open()

            # 显示拍照提示
            self.show_info("请拍摄清晰的健康资料")

        except ImportError:
            print("缺少相机功能所需的依赖库")
            self.show_error("相机功能需要安装OpenCV库，请检查环境配置")
        except Exception as e:
            print(f"启动相机失败: {e}")
            import traceback
            traceback.print_exc()
            self.show_error("相机功能初始化失败，请检查设备权限")

    def handle_camera_capture(self, result):
        """处理相机拍摄的图片

        Args:
            result: 包含图像路径或错误信息的字典
        """
        try:
            # 检查结果
            if not result:
                self.show_error("相机返回的结果为空")
                return

            # 检查是否有错误
            if isinstance(result, dict) and 'error' in result:
                self.show_error(f"拍照出错: {result['error']}")
                return

            # 检查图像路径
            if not isinstance(result, dict) or 'photo_path' not in result:
                self.show_error("未能获取照片路径")
                return

            image_path = result['photo_path']
            print(f"获取到相机拍摄的图片: {image_path}")

            # 检查文件是否存在
            if not os.path.exists(image_path):
                self.show_error(f"图片文件不存在: {image_path}")
                return

            # 显示加载对话框
            self.show_loading_dialog("正在处理照片...")

            # 启动后台线程处理照片
            import threading
            thread = threading.Thread(target=lambda: self.process_photo_in_background(image_path))
            thread.daemon = True
            thread.start()
        except Exception as e:
            print(f"处理相机拍摄的图片出错: {e}")
            import traceback
            traceback.print_exc()
            self.dismiss_loading_dialog()
            self.show_error(f"处理照片时出错: {str(e)}")

    def process_photo_in_background(self, image_path):
        """在后台处理拍摄的照片"""
        try:
            # 导入需要的模块
            from api.api_client import APIClient
            from utils.cloud_api import get_cloud_api
            from utils.user_manager import get_user_manager

            # 获取云API和用户管理器
            cloud_api = get_cloud_api()
            user_manager = get_user_manager()

            # 获取用户custom_id
            custom_id = user_manager.get_current_user_custom_id()
            print(f"当前用户custom_id: {custom_id}")

            # 检查用户是否有custom_id
            if not custom_id:
                print("用户没有custom_id，无法上传照片")
                # 尝试加载认证信息
                cloud_api.load_auth_info()

                # 再次检查是否有custom_id
                if hasattr(cloud_api, 'custom_id') and cloud_api.custom_id:
                    custom_id = cloud_api.custom_id
                    print(f"从cloud_api获取到custom_id: {custom_id}")
                    # 更新用户的custom_id
                    if user_manager.current_user:
                        user_manager.current_user.custom_id = custom_id
                        user_manager.save_current_user(force_save=True)
                        print(f"已更新用户的custom_id: {custom_id}")
                else:
                    # 检查用户是否已登录
                    if not cloud_api.is_authenticated():
                        print("用户未登录且没有custom_id，无法上传照片")
                        # 将文件添加到上传队列
                        metadata = {
                            "document_type": "medical_report",
                            "description": "移动端拍摄的照片",
                            "source": "mobile_camera"
                        }

                        # 如果有user_id，添加到元数据中
                        if user_manager.current_user and hasattr(user_manager.current_user, 'user_id'):
                            metadata["user_id"] = user_manager.current_user.user_id
                            print(f"使用user_id添加到元数据: {user_manager.current_user.user_id}")

                        queued = cloud_api.add_to_upload_queue(image_path, metadata)
                        queue_msg = "已加入上传队列，稍后将自动上传" if queued else "添加到上传队列失败"

                        # 在主线程中更新UI
                        Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
                        Clock.schedule_once(lambda dt: self.show_info(f"用户未登录，照片{queue_msg}"), 0)
                        return

            # 图像预处理
            try:
                # 导入图像处理模块
                import cv2
                import os

                # 检查文件类型
                _, file_extension = os.path.splitext(image_path)
                file_extension = file_extension.lower()

                # 图像预处理 - 对图像文件进行预处理
                image_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
                if file_extension in image_formats:
                    print(f"对相机拍摄的图像进行预处理: {image_path}")
                    # 读取图像
                    image = cv2.imread(image_path)
                    if image is not None:
                        # 调整大小，保持宽高比
                        max_dim = 2000
                        height, width = image.shape[:2]
                        if max(height, width) > max_dim:
                            scale = max_dim / max(height, width)
                            image = cv2.resize(image, None, fx=scale, fy=scale, interpolation=cv2.INTER_AREA)
                            print(f"已调整图像大小: {width}x{height} -> {int(width*scale)}x{int(height*scale)}")

                        # 增强对比度
                        lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                        l, a, b = cv2.split(lab)
                        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8, 8))
                        cl = clahe.apply(l)
                        enhanced_lab = cv2.merge((cl, a, b))
                        enhanced_image = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)

                        # 保存预处理后的图像
                        processed_path = image_path.replace('.', '_processed.')
                        cv2.imwrite(processed_path, enhanced_image)
                        print(f"已保存预处理后的图像: {processed_path}")

                        # 使用预处理后的图像路径
                        image_path = processed_path
            except ImportError:
                print("未安装OpenCV，跳过图像预处理")
            except Exception as e:
                print(f"图像预处理失败，使用原始图像: {str(e)}")

            # 初始化API客户端
            api_client = APIClient()
            print(f"初始化API客户端完成，准备处理图片: {image_path}")

            # 使用APIClient处理图片，而不是直接使用TencentOCR
            result = api_client.upload_document(image_path)
            print(f"文档处理完成，结果: {result}")

            # 检查是否有认证错误
            if isinstance(result, dict) and not result.get('success', True):
                error = result.get('error', '') or result.get('message', '')
                if 'API密钥' in error or 'API密钥' in result.get('text', '') or result.get('code') in ['AuthFailure', 'MissingApiKey']:
                    # 在主线程中调用显示API密钥错误对话框
                    Clock.schedule_once(lambda dt: self.show_api_key_error_dialog(), 0)
                    return

            # 在主线程中调用结果处理
            Clock.schedule_once(lambda dt: self.handle_ocr_result(image_path, result), 0)
        except Exception as e:
            print(f"处理照片异常: {e}")
            import traceback
            traceback.print_exc()
            # 在主线程中显示错误
            Clock.schedule_once(lambda dt: self.dismiss_loading_dialog(), 0)
            Clock.schedule_once(lambda dt: self.show_error(f"处理照片失败: {str(e)}"), 0)

    # OCR结果处理
    def handle_ocr_result(self, source_file, result):
        """处理OCR结果"""
        self.dismiss_loading_dialog()

        # 保存源文件路径
        self.source_file = source_file

        # 检查是否有错误
        if isinstance(result, dict) and not result.get('success', True):
            error = result.get('error', '')
            if 'API密钥' in error or result.get('code') in ['AuthFailure', 'MissingApiKey']:
                self.show_api_key_error_dialog()
                return
            else:
                self.show_error(f"OCR识别失败: {error}")
                return

        # 提取文本结果
        if isinstance(result, dict) and 'text' in result:
            text_result = result['text']
        elif isinstance(result, str):
            text_result = result
        else:
            text_result = str(result)

        # 保存OCR结果
        self.ocr_result = result

        # 简化，直接显示成功消息并保存结果
        self.show_success("OCR识别成功，正在保存结果...")
        self.save_ocr_result_immediate()

    def verify_document_owner(self, result):
        """验证文件所属人信息

        从OCR结果中提取姓名和性别信息，并与当前登录用户进行比对，
        确保上传的是当前用户的健康资料，避免误传他人资料。

        Args:
            result: OCR识别结果

        Returns:
            bool: 验证是否通过
        """
        try:
            # 获取当前用户信息
            from utils.user_manager import get_user_manager
            user_manager = get_user_manager()
            current_user = user_manager.get_current_user()

            if not current_user:
                self.show_error("未找到当前用户信息，请重新登录")
                return False

            # 获取当前用户的姓名和性别
            user_name = current_user.real_name or current_user.username
            user_gender = current_user.gender  # 应为"男"或"女"

            # 从OCR结果中提取姓名和性别
            ocr_text = ""
            if isinstance(result, dict) and 'text' in result:
                ocr_text = result['text']
            elif isinstance(result, str):
                ocr_text = result
            else:
                ocr_text = str(result)

            # 提取可能的姓名和性别
            # 常见的姓名标识
            name_patterns = [r"姓名[:：]\s*([\u4e00-\u9fa5]{2,5})", r"名字[:：]\s*([\u4e00-\u9fa5]{2,5})",
                           r"患者[:：]\s*([\u4e00-\u9fa5]{2,5})", r"([\u4e00-\u9fa5]{2,5})\s*先生",
                           r"([\u4e00-\u9fa5]{2,5})\s*女士"]
            # 常见的性别标识
            gender_patterns = [r"性别[:：]\s*(男|女)", r"([男女])性"]

            import re

            # 尝试提取姓名
            doc_name = None
            for pattern in name_patterns:
                matches = re.search(pattern, ocr_text)
                if matches:
                    doc_name = matches.group(1)
                    break

            # 尝试提取性别
            doc_gender = None
            for pattern in gender_patterns:
                matches = re.search(pattern, ocr_text)
                if matches:
                    doc_gender = matches.group(1)  # "男"或"女"
                    break

            # 如果未能提取到姓名或性别，直接通过验证
            if not doc_name and not doc_gender:
                print("未能从文档中提取到姓名和性别信息，默认通过验证")
                return True

            # 打印调试信息
            print(f"文档中提取的姓名: {doc_name}, 性别: {doc_gender}")
            print(f"当前用户姓名: {user_name}, 性别: {user_gender}")

            # 验证姓名（如果提取到）
            if doc_name and doc_name != user_name:
                self.show_error(f"文档所属人({doc_name})与当前用户({user_name})不匹配，请确认是否上传了他人的健康资料")
                return False

            # 验证性别（如果提取到）
            if doc_gender and user_gender and doc_gender != user_gender:
                self.show_error(f"文档性别({doc_gender})与当前用户性别({user_gender})不匹配，请确认是否上传了他人的健康资料")
                return False

            # 验证通过
            return True

        except Exception as e:
            print(f"验证文件所属人信息时出错: {e}")
            import traceback
            traceback.print_exc()
            # 出错时默认通过验证，避免阻止正常使用
            return True

    def create_ocr_result_view(self, result):
        """创建OCR结果视图"""
        # 创建可滚动视图显示OCR结果
        from kivymd.uix.boxlayout import MDBoxLayout
        from kivymd.uix.scrollview import MDScrollView
        from kivymd.uix.label import MDLabel

        # 创建容器
        container = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            height=dp(300),
            spacing=dp(10),
            padding=dp(10)
        )

        # 创建滚动视图
        scroll_view = MDScrollView(
            size_hint=(1, 1)
        )

        # 提取文本结果
        if isinstance(result, dict) and 'text' in result:
            text_result = result['text']
        elif isinstance(result, str):
            text_result = result
        else:
            text_result = str(result)

        # 创建内容布局
        content_layout = MDBoxLayout(
            orientation="vertical",
            size_hint_y=None,
            adaptive_height=True,
            padding=dp(10)
        )

        # 添加文本标签
        result_label = MDLabel(
            text=text_result,
            adaptive_height=True,
            halign="left"
        )

        # 将标签添加到内容布局
        content_layout.add_widget(result_label)

        # 添加到滚动视图
        scroll_view.add_widget(content_layout)
        container.add_widget(scroll_view)

        return container

    def save_ocr_result_immediate(self):
        """直接保存OCR结果到数据库（不显示对话框）"""
        import threading
        from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
        from kivy.metrics import dp
        from kivy.clock import Clock

        # 使用Snackbar代替对话框，这在KivyMD 2.0.1中更可靠
        snackbar = MDSnackbar(
            MDSnackbarText(
                text="正在保存识别结果...",
            ),
            pos_hint={"center_x": 0.5},
            size_hint_x=0.8,
            duration=10  # 设置较长的持续时间，直到手动关闭
        )
        self.loading_dialog = snackbar  # 存储引用以便后续关闭
        snackbar.open()

        # 启动后台线程保存结果，避免阻塞UI
        threading.Thread(target=self._save_ocr_result_thread, daemon=True).start()

    def _save_ocr_result_thread(self):
        try:
            # 准备数据
            ocr_result = self.ocr_result
            if not ocr_result:
                # 使用Clock调度到主线程
                Clock.schedule_once(lambda dt: self.show_warning("没有识别结果可保存"), 0)
                return

            # 确保OCR结果是字符串
            if not isinstance(ocr_result, str):
                ocr_result = str(ocr_result)

            app = MDApp.get_running_app()

            # 获取当前用户信息
            user_id = getattr(app, 'user_data', {}).get('user_id', '')
            if not user_id:
                Logger.error("HomepageScreen: No user ID available")
                # 使用Clock调度到主线程
                Clock.schedule_once(lambda dt: self.show_warning("无法识别当前用户，无法保存结果"), 0)
                return

            # 直接保存到文件系统
            try:
                import os
                import json
                from datetime import datetime

                # 创建OCR结果存储目录
                ocr_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'ocr_results')
                os.makedirs(ocr_dir, exist_ok=True)

                # 创建带有时间戳的文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"ocr_result_{user_id}_{timestamp}.json"
                filepath = os.path.join(ocr_dir, filename)

                # 保存OCR结果
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump({
                    'user_id': user_id,
                        'timestamp': timestamp,
                        'ocr_text': ocr_result
                    }, f, ensure_ascii=False, indent=2)

                Logger.info(f"OCR结果已保存至: {filepath}")
                success = True
            except Exception as e:
                Logger.error(f"保存OCR结果到文件时出错: {str(e)}")
                success = False

            # 关闭加载对话框 - 已经在主线程中
            Clock.schedule_once(lambda dt: self.loading_dialog.dismiss() if hasattr(self, 'loading_dialog') and self.loading_dialog else None, 0.5)

            # 显示成功消息 - 使用Clock调度到主线程
            if success:
                Clock.schedule_once(lambda dt: self.show_success("识别结果已成功保存"), 0.6)
            else:
                Clock.schedule_once(lambda dt: self.show_warning("保存识别结果失败"), 0.6)

        except Exception as e:
            Logger.error(f"HomepageScreen: Error saving OCR result: {str(e)}")
            # 使用Clock调度到主线程
            Clock.schedule_once(lambda dt: self.show_warning(f"保存过程中出错: {str(e)}"), 0)

            # 确保对话框被关闭 - 已经在主线程中
            try:
                if hasattr(self, 'loading_dialog') and self.loading_dialog:
                    Clock.schedule_once(lambda dt: self.loading_dialog.dismiss(), 0.5)
            except:
                pass

    # 界面辅助方法
    def show_loading_dialog(self, text="加载中..."):
        """显示加载对话框"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

            # 使用Snackbar代替对话框，这在KivyMD 2.0.1中更可靠
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text=text,
                ),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                duration=10  # 设置较长的持续时间，直到手动关闭
            )
            self.loading_dialog = snackbar  # 存储引用以便后续关闭
            snackbar.open()

        except Exception as e:
            # 如果创建对话框失败，至少打印错误信息
            print(f"无法显示加载提示: {e}")
            import traceback
            traceback.print_exc()

    def dismiss_loading_dialog(self):
        """关闭加载对话框"""
        self.safe_dismiss_dialog('loading_dialog')

    def show_info(self, message):
        """显示信息提示"""
        # 检查是否在主线程中
        from kivy.clock import Clock
        import threading

        def _show_info(dt=None):
            try:
                from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
                from kivy.metrics import dp

                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    y=dp(24),
                    pos_hint={"center_x": 0.5},
                    size_hint_x=0.8,
                )
                snackbar.open()
            except Exception as e:
                # 如果创建Snackbar失败，至少打印错误信息
                print(f"无法显示信息提示: {e}")
                import traceback
                traceback.print_exc()

        # 如果不在主线程中，使用Clock调度到主线程
        if threading.current_thread() is not threading.main_thread():
            Clock.schedule_once(_show_info, 0)
        else:
            _show_info()

    def show_error(self, message):
        """显示错误提示"""
        # 检查是否在主线程中
        from kivy.clock import Clock
        import threading

        def _show_error(dt=None):
            try:
                from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
                from kivy.metrics import dp

                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    y=dp(24),
                    pos_hint={"center_x": 0.5},
                    size_hint_x=0.8,
                    md_bg_color=(0.8, 0.2, 0.2, 1),  # 红色背景
                )
                snackbar.open()
            except Exception as e:
                # 如果创建Snackbar失败，至少打印错误信息
                print(f"无法显示错误提示: {e}")
                import traceback
                traceback.print_exc()

        # 如果不在主线程中，使用Clock调度到主线程
        if threading.current_thread() is not threading.main_thread():
            Clock.schedule_once(_show_error, 0)
        else:
            _show_error()

    def show_warning(self, message):
        """显示警告提示"""
        # 检查是否在主线程中
        from kivy.clock import Clock
        import threading

        def _show_warning(dt=None):
            try:
                from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
                from kivy.metrics import dp

                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    y=dp(24),
                    pos_hint={"center_x": 0.5},
                    size_hint_x=0.8,
                    md_bg_color=(0.9, 0.6, 0.0, 1),  # 橙色背景
                )
                snackbar.open()
            except Exception as e:
                # 如果创建Snackbar失败，至少打印错误信息
                print(f"无法显示警告提示: {e}")
                import traceback
                traceback.print_exc()

        # 如果不在主线程中，使用Clock调度到主线程
        if threading.current_thread() is not threading.main_thread():
            Clock.schedule_once(_show_warning, 0)
        else:
            _show_warning()

    def show_success(self, message):
        """显示成功提示"""
        # 检查是否在主线程中
        from kivy.clock import Clock
        import threading

        def _show_success(dt=None):
            try:
                from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
                from kivy.metrics import dp

                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text=message,
                    ),
                    y=dp(24),
                    pos_hint={"center_x": 0.5},
                    size_hint_x=0.8,
                    md_bg_color=(0.2, 0.7, 0.2, 1),  # 绿色背景
                )
                snackbar.open()
            except Exception as e:
                # 如果创建Snackbar失败，至少打印错误信息
                print(f"无法显示成功提示: {e}")
                import traceback
                traceback.print_exc()

        # 如果不在主线程中，使用Clock调度到主线程
        if threading.current_thread() is not threading.main_thread():
            Clock.schedule_once(_show_success, 0)
        else:
            _show_success()

    def show_api_key_error_dialog(self):
        """显示API密钥错误对话框，引导用户设置API密钥"""
        try:
            from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText
            from kivy.clock import Clock

            # 显示简单的提示
            snackbar = MDSnackbar(
                MDSnackbarText(
                    text="需要配置API密钥才能使用OCR功能",
                ),
                pos_hint={"center_x": 0.5},
                size_hint_x=0.8,
                duration=5
            )
            snackbar.open()

            # 延迟一会后自动跳转到OCR设置页面
            Clock.schedule_once(self.go_to_ocr_settings, 2)

        except Exception as e:
            print(f"无法显示API密钥错误提示: {e}")
            import traceback
            traceback.print_exc()
            # 尝试直接跳转到OCR设置页
            Clock.schedule_once(self.go_to_ocr_settings, 0.5)

    def go_to_ocr_settings(self, *args):
        """显示云端OCR信息对话框"""
        try:
            # 显示云端OCR服务信息
            from kivymd.uix.boxlayout import MDBoxLayout
            from kivymd.uix.label import MDLabel
            from kivymd.uix.button import MDButton, MDButtonText
            from kivymd.uix.dialog import MDDialog, MDDialogTitle, MDDialogContent, MDDialogButtonContainer
            from kivy.metrics import dp

            content = MDBoxLayout(
                orientation='vertical',
                spacing=dp(10),
                padding=[dp(20), dp(10), dp(20), dp(10)],
                adaptive_height=True
            )

            content.add_widget(MDLabel(
                text="云端OCR服务信息",
                font_style="H6",
                halign="center"
            ))

            content.add_widget(MDLabel(
                text="本应用使用云端OCR服务自动识别文档内容，无需本地设置。\n\n服务器地址：http://8.138.188.26/api\n\n所有文档识别和处理均在云端完成，确保高效准确的识别结果。",
                font_style="Body",
                halign="left"
            ))

            dialog = MDDialog(
                MDDialogTitle(
                    text="云端OCR服务",
                ),
                MDDialogContent(
                    content,
                ),
                MDDialogButtonContainer(
                    MDButton(
                        MDButtonText(text="确定"),
                        style="text",
                        on_release=lambda x: dialog.dismiss()
                    )
                ),
            )
            dialog.open()
        except Exception as e:
            # 如果创建对话框失败，至少打印错误信息
            print(f"无法显示OCR设置对话框: {e}")
            import traceback
            traceback.print_exc()

            # 使用Snackbar作为备选方案
            try:
                from kivymd.uix.snackbar.snackbar import MDSnackbar, MDSnackbarText

                snackbar = MDSnackbar(
                    MDSnackbarText(
                        text="云端OCR服务已配置，无需本地设置",
                    ),
                    pos_hint={"center_x": 0.5},
                    size_hint_x=0.8,
                    duration=5
                )
                snackbar.open()
            except:
                pass

    def save_ocr_result(self):
        """保存OCR结果到数据库（兼容性方法）"""
        # 安全关闭结果对话框（如果存在）
        self.safe_dismiss_dialog('result_dialog')
        # 调用新方法
        self.save_ocr_result_immediate()

if __name__ == '__main__':
    # 用于单独测试此屏幕
    class TestApp(MDApp):
        theme = AppTheme
        metrics = AppMetrics
        font_styles = FontStyles

        def build(self):
            return HomepageScreen()

    TestApp().run()