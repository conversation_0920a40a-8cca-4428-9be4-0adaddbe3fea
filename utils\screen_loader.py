"""
屏幕懒加载管理器
提供按需加载屏幕的功能，减少应用启动时间
"""
import logging
from kivy.app import App
from kivy.clock import Clock

logger = logging.getLogger(__name__)

class ScreenLoader:
    """屏幕懒加载管理器"""

    def __init__(self):
        self._loaded_screens = set()
        self._screen_configs = {
            'homepage_screen': {
                'module': 'screens.homepage_screen',
                'class': 'HomepageScreen',
                'name': 'homepage_screen'
            },
            'register': {
                'module': 'screens.register_screen',
                'class': 'RegisterScreen',
                'name': 'register'
            },
            'profile': {
                'module': 'screens.profile_page',
                'class': 'ProfilePage',
                'name': 'profile'
            },
            'survey_screen': {
                'module': 'screens.survey_screen',
                'class': 'SurveyScreen',
                'name': 'survey_screen'
            },
            'log_screen': {
                'module': 'screens.log_screen',
                'class': 'LogScreen',
                'name': 'log_screen'
            },
            'public_screen': {
                'module': 'screens.public_screen',
                'class': 'PublicScreen',
                'name': 'public_screen'
            },
            'consultant_screen': {
                'module': 'screens.consultant_screen',
                'class': 'ConsultantScreen',
                'name': 'consultant_screen'
            },
            'unit_screen': {
                'module': 'screens.unit_screen',
                'class': 'UnitScreen',
                'name': 'unit_screen'
            },
            'supermanager_screen': {
                'module': 'screens.supermanager_screen',
                'class': 'SuperManagerScreen',
                'name': 'supermanager_screen'
            },
            'primaryhealth_screen': {
                'module': 'screens.primaryhealth_screen',
                'class': 'UploadScreen',
                'name': 'primaryhealth_screen'
            },
            'generalhealth_screen': {
                'module': 'screens.generalhealth_screen',
                'class': 'GeneralHealthScreen',
                'name': 'generalhealth_screen'
            },
            'document_list_screen': {
                'module': 'screens.document_list_screen',
                'class': 'DocumentListScreen',
                'name': 'document_list_screen'
            },
            'voice_triage_screen': {
                'module': 'screens.voice_triage_screen',
                'class': 'VoiceTriageScreen',
                'name': 'voice_triage_screen'
            }
        }

    def load_screen(self, screen_name):
        """
        懒加载指定的屏幕

        Args:
            screen_name: 屏幕名称

        Returns:
            bool: 是否成功加载
        """
        if screen_name in self._loaded_screens:
            logger.debug(f"屏幕 {screen_name} 已经加载")
            return True

        if screen_name not in self._screen_configs:
            logger.error(f"未知的屏幕名称: {screen_name}")
            return False

        config = self._screen_configs[screen_name]

        try:
            # 动态导入模块
            module = __import__(config['module'], fromlist=[config['class']])
            screen_class = getattr(module, config['class'])

            # 创建屏幕实例
            screen_instance = screen_class(name=config['name'])

            # 添加到屏幕管理器
            app = App.get_running_app()
            if app and hasattr(app, 'root'):
                app.root.add_widget(screen_instance)
                self._loaded_screens.add(screen_name)
                logger.info(f"成功加载屏幕: {screen_name}")
                return True
            else:
                logger.error("无法获取应用实例或屏幕管理器")
                return False

        except ImportError as e:
            logger.error(f"导入屏幕模块失败 {config['module']}: {e}")
            return False
        except AttributeError as e:
            logger.error(f"屏幕类不存在 {config['class']}: {e}")
            return False
        except Exception as e:
            logger.error(f"加载屏幕时出错 {screen_name}: {e}")
            return False

    def load_screen_async(self, screen_name, callback=None):
        """
        异步加载屏幕

        Args:
            screen_name: 屏幕名称
            callback: 加载完成后的回调函数
        """
        def _load_screen(dt):
            success = self.load_screen(screen_name)
            if callback:
                callback(screen_name, success)

        Clock.schedule_once(_load_screen, 0)

    def preload_common_screens(self):
        """
        预加载常用屏幕
        """
        common_screens = [
            'homepage_screen',
            'register',
            'profile'
        ]

        def _preload_next(dt):
            if common_screens:
                screen_name = common_screens.pop(0)
                self.load_screen_async(screen_name,
                    lambda name, success: Clock.schedule_once(_preload_next, 0.1) if common_screens else None)

        Clock.schedule_once(_preload_next, 1.0)  # 延迟1秒开始预加载

    def is_screen_loaded(self, screen_name):
        """
        检查屏幕是否已加载

        Args:
            screen_name: 屏幕名称

        Returns:
            bool: 是否已加载
        """
        return screen_name in self._loaded_screens

    def get_loaded_screens(self):
        """
        获取已加载的屏幕列表

        Returns:
            set: 已加载的屏幕名称集合
        """
        return self._loaded_screens.copy()

# 全局屏幕加载器实例
_screen_loader = None

def get_screen_loader():
    """
    获取全局屏幕加载器实例

    Returns:
        ScreenLoader: 屏幕加载器实例
    """
    global _screen_loader
    if _screen_loader is None:
        _screen_loader = ScreenLoader()
    return _screen_loader

def load_screen_if_needed(screen_name):
    """
    如果需要则加载屏幕的便捷函数

    Args:
        screen_name: 屏幕名称

    Returns:
        bool: 是否成功加载或已经加载
    """
    loader = get_screen_loader()
    if loader.is_screen_loaded(screen_name):
        return True
    return loader.load_screen(screen_name)
