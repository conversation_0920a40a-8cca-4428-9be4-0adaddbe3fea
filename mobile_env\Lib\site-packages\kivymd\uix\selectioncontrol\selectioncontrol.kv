<MDCheckbox>
    _current_color:
        ( \
        ( \
        self.theme_cls.primaryColor \
        if not self.color_active else self.color_active \
        ) \
        if self.active else \
        ( \
        self.theme_cls.onSurfaceVariantColor \
        if not self.color_inactive else self.color_inactive \
        ) \
        ) \
        if not self.disabled else \
        ( \
        self.theme_cls.onSurfaceColor[:-1] \
        + [self.checkbox_opacity_value_disabled_container] \
        if not self.color_disabled else self.color_disabled \
        )

    canvas:
        Color:
            rgba: self._current_color
        Rectangle:
            texture: self.texture
            size: self.texture_size
            pos:
                int(self.center_x - self.texture_size[0] / 2.), \
                int(self.center_y - self.texture_size[1] / 2.)

    # FIXME: Move to `kivymd/uix/behaviors/state_layer_behavior.py`
    canvas.after:
        # Clear
        Color
            rgba: self.state_layer_color
        RoundedRectangle:
            size: self.width + dp(20), self.height + dp(20)
            pos: self.x - self.width / 2 + dp(2), self.y - self.height / 2 + dp(2)
            radius: [(self.width + dp(20)) / 2, ]

    theme_text_color: "Custom"
    text_color: self._current_color
    halign: "center"
    valign: "middle"
    size_hint: None, None
    size: "22dp", "22dp"


<Thumb>
    radius: self.width / 2
    ripple_scale: 2

    ThumbIcon:
        id: icon
        font_size: "16sp"
        pos_hint: {"center_x": .5, "center_y": .5}
        theme_text_color: "Custom"
        text_color:
            ( \
            ( \
            ( \
            root.parent.icon_active_color \
            if root.parent.icon_active_color \
            else self.theme_cls.onPrimaryContainerColor \
            ) \
            if root.parent.icon_active and root.parent.active else \
            ( \
            root.parent.icon_inactive_color \
            if root.parent.icon_inactive_color \
            else self.theme_cls.surfaceContainerHighestColor \
            ) \
            ) \
            if not root.parent.disabled else \
            ( \
            self.theme_cls.onSurfaceColor[:-1] \
            + [root.parent.switch_opacity_value_disabled_icon] \
            if root.parent.icon_active else \
            self.theme_cls.surfaceContainerHighestColor[:-1] \
            + [root.parent.switch_opacity_value_disabled_icon] \
            ) \
            ) \
            if root.parent else self.theme_cls.transparentColor


<MDSwitch>
    size_hint: None, None
    size: dp(52), dp(32)
    radius: [self.height / 2, ]
    md_bg_color:
        ( \
        self.track_color_disabled \
        if self.track_color_disabled else \
        ( \
        self.theme_cls.onSurfaceColor[:-1] \
        + [self.switch_opacity_value_disabled_container] \
        if self.active else self.theme_cls.surfaceContainerHighestColor[:-1] \
        + [self.switch_opacity_value_disabled_container] \
        ) \
        ) \
        if self.disabled else \
        ( \
        ( \
        self.track_color_active \
        if self.track_color_active else \
        self.theme_cls.primaryColor \
        ) \
        if self.active else \
        (self.track_color_inactive \
        if self.track_color_inactive else \
        self.theme_cls.surfaceContainerHighestColor \
        ) \
        )
    line_color:
        ( \
        ( \
        self.theme_cls.outlineColor if not self.active else self.md_bg_color
        ) \
        if not self.disabled else \
        self.theme_cls.onSurfaceColor[:-1] \
        + [self.switch_opacity_value_disabled_line] \
        ) \
        if self.theme_line_color == "Primary" else \
        self._line_color if not self.disabled else \
        ( \
        self.line_color_disabled \
        if self.line_color_disabled else \
        self._line_color \
        )

    on_press:
        if not self.disabled: self.on_thumb_down()
    on_release:
        if not self.disabled: setattr(self, "active", not self.active)

    Thumb:
        id: thumb
        size_hint: None, None
        size: dp(24), dp(24)
        _no_ripple_effect: not root.ripple_effect
        md_bg_color:
            ( \
            root.thumb_color_disabled \
            if root.thumb_color_disabled else \
            ( \
            root.theme_cls.surfaceColor \
            if root.active else root.theme_cls.onSurfaceColor[:-1] \
            + [root.switch_thumb_opacity_value_disabled_container] \
            ) \
            ) \
            if root.disabled else \
            ( \
            (root.thumb_color_active \
            if root.thumb_color_active else \
            root.theme_cls.onPrimaryColor \
            ) \
            if root.active else \
            ( \
            root.thumb_color_inactive \
            if root.thumb_color_inactive else \
            self.theme_cls.outlineColor \
            ) \
            )
        on_press:
            if not root.disabled: root.dispatch("on_press")
        on_release:
            if not root.disabled: root.dispatch("on_release")
        pos:
            root.pos[0] + (self.width / 2) + root._thumb_pos[0] \
            + dp(6 if root.icon_inactive else 0), \
            root.pos[1] + (root.height / 2 - self.height / 2) + root._thumb_pos[1]