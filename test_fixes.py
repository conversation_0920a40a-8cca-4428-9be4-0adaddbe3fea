#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复的功能
"""

import os
import sys

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_password_visibility():
    """测试密码显示/隐藏功能"""
    print("=== 测试密码显示/隐藏功能 ===")

    try:
        # 模拟密码字段和图标
        class MockPasswordField:
            def __init__(self):
                self.password = True
                self.children = [MockTrailingIcon()]

        class MockTrailingIcon:
            def __init__(self):
                self.icon = "eye-off"
                self.theme_icon_color = "Custom"
                self.icon_color = [0, 0, 1, 1]
                self._callbacks = []

            def bind(self, **kwargs):
                for event, callback in kwargs.items():
                    self._callbacks.append((event, callback))
                    print(f"绑定事件 {event} 成功")

        class MockLoginTab:
            def __init__(self):
                self.ids = type('obj', (object,), {
                    'password': MockPasswordField(),
                    'password_icon': MockTrailingIcon()
                })()

            def toggle_password_visibility(self):
                """切换密码可见性"""
                try:
                    password_field = self.ids.password
                    trailing_icon = None

                    # 查找trailing icon
                    for child in password_field.children:
                        if type(child).__name__ == 'MockTrailingIcon':
                            trailing_icon = child
                            break

                    if not trailing_icon and hasattr(self.ids, 'password_icon'):
                        trailing_icon = self.ids.password_icon

                    if trailing_icon:
                        current_icon = getattr(trailing_icon, 'icon', 'eye-off')
                        if current_icon == "eye-off":
                            trailing_icon.icon = "eye"
                            password_field.password = False
                            print("密码已显示")
                        else:
                            trailing_icon.icon = "eye-off"
                            password_field.password = True
                            print("密码已隐藏")

                        print(f"密码可见性已切换: {'隐藏' if password_field.password else '显示'}")
                        return True
                    else:
                        print("未找到密码图标，无法切换可见性")
                        return False

                except Exception as e:
                    print(f"切换密码可见性时出错: {e}")
                    return False

        # 测试密码可见性切换
        login_tab = MockLoginTab()

        print("初始状态:")
        print(f"密码隐藏: {login_tab.ids.password.password}")
        print(f"图标: {login_tab.ids.password.children[0].icon}")

        print("\n第一次切换:")
        result1 = login_tab.toggle_password_visibility()
        print(f"切换结果: {result1}")
        print(f"密码隐藏: {login_tab.ids.password.password}")
        print(f"图标: {login_tab.ids.password.children[0].icon}")

        print("\n第二次切换:")
        result2 = login_tab.toggle_password_visibility()
        print(f"切换结果: {result2}")
        print(f"密码隐藏: {login_tab.ids.password.password}")
        print(f"图标: {login_tab.ids.password.children[0].icon}")

        if result1 and result2:
            print("✅ 密码显示/隐藏功能测试通过")
        else:
            print("❌ 密码显示/隐藏功能测试失败")

    except Exception as e:
        print(f"❌ 密码显示/隐藏功能测试出错: {e}")
        import traceback
        traceback.print_exc()

def test_role_selection():
    """测试角色选择功能"""
    print("\n=== 测试角色选择功能 ===")

    try:
        # 模拟注册屏幕
        class MockRegisterScreen:
            def __init__(self):
                self.selected_roles = []
                self.registration_type = "本人注册"
                self.identity = ""
                self.selected_role_color = [0.2, 0.6, 1, 0.8]
                self._reg_trigger = False

                # 模拟角色卡片
                self.ids = type('obj', (object,), {
                    'personal_user_card': MockRoleCard("个人用户"),
                    'unit_admin_card': MockRoleCard("单位管理员"),
                    'health_advisor_card': MockRoleCard("健康顾问"),
                    'super_admin_card': MockRoleCard("超级管理员")
                })()

            def select_role(self, role):
                """选择或取消选择角色"""
                print(f"角色按钮被点击: {role}")

                was_health_advisor = "健康顾问" in self.selected_roles
                roles_list = list(self.selected_roles)

                # 如果是替他人注册，只能选择个人用户角色
                if self.registration_type == "替他人注册":
                    if role != "个人用户":
                        print("替他人注册只能选择'个人用户'角色")
                        return
                    elif "个人用户" not in roles_list:
                        roles_list = ["个人用户"]
                        self.selected_roles = roles_list
                        self.identity = "个人用户"
                        self._trigger_ui_update()
                        return
                    elif role == "个人用户" and "个人用户" in roles_list:
                        print("替他人注册必须选择'个人用户'角色")
                        return
                else:
                    # 正常角色选择/取消逻辑
                    if role in roles_list:
                        # 取消选择
                        roles_list.remove(role)
                        print(f"取消选择角色: {role}")
                        if role == self.identity and roles_list:
                            # 重新选择身份
                            if "超级管理员" in roles_list:
                                self.identity = "超级管理员"
                            elif "健康顾问" in roles_list:
                                self.identity = "健康顾问"
                            elif "单位管理员" in roles_list:
                                self.identity = "单位管理员"
                            else:
                                self.identity = "个人用户"
                        elif role == self.identity and not roles_list:
                            self.identity = ""
                    else:
                        # 选择角色
                        roles_list.append(role)
                        print(f"选择角色: {role}")
                        if not self.identity or (
                            role == "超级管理员" or
                            (role == "健康顾问" and self.identity not in ["超级管理员"]) or
                            (role == "单位管理员" and self.identity not in ["超级管理员", "健康顾问"])
                        ):
                            self.identity = role

                # 更新角色列表
                self.selected_roles = roles_list
                print(f"当前选择的角色: {self.selected_roles}, 身份: {self.identity}")

                # 触发UI更新
                self._trigger_ui_update()

                # 检查健康顾问状态变化
                is_health_advisor = "健康顾问" in roles_list
                if was_health_advisor != is_health_advisor:
                    print(f"健康顾问状态改变: {is_health_advisor}")

            def _trigger_ui_update(self):
                """触发UI更新"""
                self._reg_trigger = not self._reg_trigger
                self._update_role_cards_visual_state()

            def _update_role_cards_visual_state(self):
                """更新角色卡片视觉状态"""
                role_cards = {
                    "个人用户": "personal_user_card",
                    "单位管理员": "unit_admin_card",
                    "健康顾问": "health_advisor_card",
                    "超级管理员": "super_admin_card"
                }

                for role, card_id in role_cards.items():
                    if hasattr(self.ids, card_id):
                        card = getattr(self.ids, card_id)
                        is_selected = role in self.selected_roles
                        is_disabled = (self.registration_type == "替他人注册" and role != "个人用户")

                        # 更新卡片状态
                        if is_selected and not is_disabled:
                            card.md_bg_color = self.selected_role_color
                            card.elevation = 3
                            card.ripple_behavior = False  # 禁用ripple效果
                        else:
                            card.md_bg_color = [1, 1, 1, 1]  # 白色背景
                            card.elevation = 1
                            card.ripple_behavior = False  # 禁用ripple效果

                        card.disabled = is_disabled
                        card.opacity = 0.5 if is_disabled else 1.0

                        # 模拟延迟重新设置背景色以确保持续保留
                        def delayed_color_update(card_ref, color):
                            card_ref.md_bg_color = color
                            print(f"延迟更新卡片颜色: {color}")

                        # 模拟Clock.schedule_once的效果
                        delayed_color_update(card, card.md_bg_color)

                        print(f"更新角色卡片 {role}: 选中={is_selected}, 禁用={is_disabled}, 颜色保留={card.md_bg_color}")

        class MockRoleCard:
            def __init__(self, role_name):
                self.role_name = role_name
                self.md_bg_color = [1, 1, 1, 1]
                self.elevation = 1
                self.disabled = False
                self.opacity = 1.0
                self.ripple_behavior = True

        # 测试角色选择
        screen = MockRegisterScreen()

        print("测试1: 选择健康顾问角色")
        screen.select_role("健康顾问")
        assert "健康顾问" in screen.selected_roles
        assert screen.identity == "健康顾问"
        health_card = screen.ids.health_advisor_card
        assert health_card.md_bg_color == screen.selected_role_color
        assert health_card.ripple_behavior == False

        print("\n测试2: 再选择个人用户角色")
        screen.select_role("个人用户")
        assert "个人用户" in screen.selected_roles
        assert "健康顾问" in screen.selected_roles
        assert screen.identity == "健康顾问"  # 健康顾问权限更高
        personal_card = screen.ids.personal_user_card
        assert personal_card.md_bg_color == screen.selected_role_color

        print("\n测试3: 取消选择健康顾问角色")
        screen.select_role("健康顾问")
        assert "健康顾问" not in screen.selected_roles
        assert "个人用户" in screen.selected_roles
        assert screen.identity == "个人用户"
        # 健康顾问卡片应该恢复默认颜色
        assert health_card.md_bg_color == [1, 1, 1, 1]

        print("\n测试4: 切换到替他人注册模式")
        screen.registration_type = "替他人注册"
        screen.select_role("健康顾问")  # 应该被拒绝
        assert "健康顾问" not in screen.selected_roles

        print("\n测试5: 验证颜色持续保留机制")
        screen.registration_type = "本人注册"
        screen.select_role("健康顾问")
        # 验证选中状态的颜色保留
        assert health_card.md_bg_color == screen.selected_role_color
        assert health_card.ripple_behavior == False  # 确保ripple被禁用

        print("✅ 角色选择功能测试通过")
        print("✅ 颜色持续保留机制测试通过")

    except Exception as e:
        print(f"❌ 角色选择功能测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始测试修复的功能...")
    test_password_visibility()
    test_role_selection()
    print("\n测试完成!")
