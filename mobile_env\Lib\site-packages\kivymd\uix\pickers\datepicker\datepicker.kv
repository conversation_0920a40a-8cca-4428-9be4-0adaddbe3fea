#:import Clock kivy.clock.Clock

###############################################################################
#
#                               MODAL INPUT RULES
#
###############################################################################

<MDModalInputDatePickerInputField>
    mode: "outlined"
    validator: "date"


<MDModalInputDatePicker>
    orientation: "vertical"
    padding: 0, dp(16), 0, dp(16)
    size_hint: None, None
    size: dp(328), dp(280)
    pos_hint: {"center_x": .5, "center_y": .5}

    MDLabel:
        id: supporting_label
        adaptive_size: True
        text: root.supporting_text
        padding: dp(16), 0, 0, dp(36)
        font_style: "Label"
        role: "large"

    BoxLayout:
        size_hint_y: None
        height: self.minimum_height
        padding: dp(16), 0, dp(8),  dp(16)

        MDLabel:
            id: current_month_name
            adaptive_size: True
            text: root._current_month_name
            text_color: self.theme_cls.onSurfaceVariantColor
            font_style: "Headline"
            role: "large"
            theme_line_height: "Custom"
            line_height: 1
            theme_font_size: "Custom"
            font_size: "32sp" if root.mode == "picker" else "20sp"
            pos_hint: {"center_y": .5}

        Widget:

        MDIconButton:
            id: icon_button
            icon: "calendar"
            on_release: root.dispatch("on_edit")

    MDDivider:

    MDModalInputDatePickerInputDateFieldContainer
        id: input_date_container
        padding: dp(16), 0, dp(16), dp(24)

        MDModalInputDatePickerInputField:
            id: input_date_field
            date_picker: root
            date_format: root.date_format

            MDTextFieldHintText:
                text: root.date_format

            MDTextFieldHelperText:
                text: root.error_text
                mode: "on_error"

    MDDatePickerButtonsContainer:
        date_picker: root

###############################################################################
#
#                                 MODAL RULES
#
###############################################################################

<MDModalDatePickerYearSelectableItem>
    size_hint_x: None
    valign: "middle"
    halign: "center"
    text_color:
        self.theme_cls.onSurfaceColor \
        if not root.selected else \
        self.theme_cls.onPrimaryColor

    canvas.before:
        Color:
            rgba:
                self.theme_cls.primaryColor \
                if self.selected else \
                self.theme_cls.transparentColor
        RoundedRectangle:
            pos: self.x + dp(12), self.y + dp(6)
            size: self.width - dp(24), self.height - dp(8)
            radius: [(root.height / 2) - dp(4), ]


<MDModalDatePickerMenuYearSelection>
    scale_value_x: 1.5
    scale_value_y: 1.5
    key_viewclass: "viewclass"
    key_size: "height"
    bar_width: 0

    MDModalDatePickerContainerMenuYearSelection:
        cols: 3
        padding: dp(32), 0, 0, 0
        default_size: None, dp(48)
        default_size_hint: 1, None
        size_hint_y: None
        height: self.minimum_height
        multiselect: False
        touch_multiselect: True


<MDModalDatePickerScrim>
    canvas:
        Color:
            rgba: self.color[:-1] + [self.alpha]
        Rectangle:
            pos: self.pos
            size: self.size


<MDModalDatePickerYearSelectionItem>
    size_hint_y: None
    height: dp(48)

    MDLabel:
        id: label
        adaptive_size: True
        text: root.text
        pos_hint: {"center_y": .5}
        padding: 0, 0, "8dp", 0
        font_style: "Label"
        role: "large"

    MDDatePickerBaseMenuSelectionButton
        id: menu_selection_button
        pos_hint: {"center_y": .5}
        icon: "menu-right"
        date_picker: root.date_picker
        on_release: root.dispatch("on_open_menu")

    Widget:

    MDIconButton:
        id: chevron_left
        icon: "chevron-left"
        pos_hint: {"center_y": .5}
        on_release: root.dispatch("on_release", "prev")

    MDIconButton:
        id: chevron_right
        icon: "chevron-right"
        pos_hint: {"center_y": .5}
        on_release: root.dispatch("on_release", "next")


<MDModalDatePicker>
    calendar_layout: calendar_layout
    orientation: "vertical"
    padding: 0, 0, 0, "12dp"
    pos_hint: {'center_x': .5, 'center_y': .5}
    size_hint: None, None
    size: calendar_layout.width - self.padding[0] / 2, dp(520)

    MDLabel:
        id: supporting_label
        adaptive_size: True
        text: root.supporting_text
        padding: dp(24), 0, 0, dp(36)
        font_style: "Label"
        role: "large"

    BoxLayout:
        size_hint_y: None
        height: self.minimum_height
        padding: dp(24), 0, dp(12),  dp(16)

        MDLabel:
            id: current_month_name
            adaptive_size: True
            text: root._current_month_name
            text_color: self.theme_cls.onSurfaceVariantColor
            font_style: "Headline"
            role: "large"

        Widget:

        MDIconButton:
            id: icon_button
            icon: "pencil"
            on_release: root.dispatch("on_edit")

    MDDivider:

    MDModalDatePickerYearSelectionItem:
        id: year_selection_items
        text: root._current_full_month_name
        date_picker: root
        padding: dp(24), 0, dp(12), 0

    RelativeLayout:
        size_hint: None, None
        size: calendar_layout.size

        MDCalendarLayout:
            id: calendar_layout
            padding: dp(12), 0, dp(12), 0

        MDModalDatePickerMenuYearSelection
            id: year_selection_layout
            size_hint: None, None
            size: 0, 0

    MDDatePickerButtonsContainer:
        date_picker: root

###############################################################################
#
#                                DOCKED RULES
#
###############################################################################

<MDDockedDatePicker>
    calendar_layout: calendar_layout
    size_hint: None, None
    size: calendar_layout.width - self.padding[0] / 2, dp(406)
    orientation: "vertical"
    padding: "12dp", 0, "12dp", "12dp"

    BoxLayout:
        id: month_year_selection_items_container
        size_hint_y: None
        height: self.minimum_height
        padding: 0, 0, 0, "12dp"

        MDDockedDatePickerMonthSelectionItem:
            id: month_selection_items
            text: root._current_month_name
            date_picker: root

        Widget:

        MDDockedDatePickerYearSelectionItem:
            id: year_selection_items
            text: str(root.year)
            date_picker: root

    RelativeLayout:
        size_hint: None, None
        size: calendar_layout.size

        MDCalendarLayout:
            id: calendar_layout

        MDDockedDatePickerMenuMonthYearSelection
            id: month_year_selection_layout
            size_hint: None, None
            size: 0, 0

    MDDatePickerButtonsContainer:
        id: button_container
        date_picker: root


<MDDockedDatePickerMenuMonthYearSelection>
    scale_value_x: 0
    scale_value_y: 0
    key_viewclass: "viewclass"
    key_size: "height"
    bar_width: 0

    MDDockedDatePickerContainerMenuMonthYearSelection:
        padding: dp(8), 0, 0, 0
        default_size: None, dp(48)
        default_size_hint: 1, None
        size_hint_y: None
        height: self.minimum_height
        orientation: "vertical"
        multiselect: False
        touch_multiselect: True


<MDDockedDatePickerMenuSelectionItem>
    spacing: "8dp"
    size_hint_y: None

    MDIcon:
        icon: "check"
        pos_hint: {"center_y": .5}
        icon_color:
            self.theme_cls.transparentColor \
            if not root.selected else \
            self.theme_cls.onSurfaceColor

    MDLabel:
        text: root.month_year_name


<MDDockedDatePickerBaseSelectionContainer>
    size_hint: None, None
    size: self.minimum_size

    MDIconButton:
        id: chevron_left
        icon: "chevron-left"
        on_release: root.dispatch("on_release", "prev")

    MDLabel:
        id: label
        adaptive_size: True
        text: root.text
        pos_hint: {"center_y": .5}
        padding: 0, 0, "8dp", 0
        font_style: "Label"
        role: "large"

    MDDatePickerBaseMenuSelectionButton
        id: menu_selection_button
        pos_hint: {"center_y": .5}
        icon: "menu-right"
        date_picker: root.date_picker
        on_release: root.dispatch("on_open_menu")

    MDIconButton:
        id: chevron_right
        icon: "chevron-right"
        on_release: root.dispatch("on_release", "next")

###############################################################################
#
#                                COMMON RULES
#
###############################################################################

<MDBaseDatePicker>
    elevation_level: 0
    shadow_color: self.theme_cls.transparentColor

    canvas.before:
        Color:
            rgba:
                self.theme_cls.surfaceContainerHighColor
        RoundedRectangle:
            size: self.size
            pos: self.pos
            radius: self.radius


<MDCalendarLayout>
    cols: 7
    size_hint: None, None
    width: dp(46 * 7)
    height: self.minimum_height


<MDDatePickerButtonsContainer>
    size_hint_y: None
    height: self.minimum_height
    padding: 0, 0, dp(12), 0

    Widget:

    MDButton:
        style: "text"
        on_release: root.date_picker.dispatch("on_cancel")

        MDButtonText:
            text:
                root.date_picker.text_button_cancel \
                if root.date_picker else \
                ""

    MDButton:
        style: "text"
        on_release: root.date_picker.dispatch("on_ok")

        MDButtonText:
            text:
                root.date_picker.text_button_ok \
                if root.date_picker else \
                ""


<MDDatePickerMenuButton>
    icon: "menu-right"


<MDDatePickerWeekdayLabel>
    size_hint: None, None
    text_size: self.size
    halign: "center"
    valign: "middle"
    size: dp(40), dp(40)


<MDDatePickerDaySelectableItem>
    size_hint: None, None
    size: dp(42), dp(42)
    halign: "center"
    radius: self.height / 2
    line_color:
        self.theme_cls.primaryColor \
        if self.is_today and self.date_picker.mark_today else \
        self.theme_cls.transparentColor
    text_color:
        self.theme_cls.onSurfaceColor \
        if not root.is_selected else \
        self.theme_cls.onPrimaryColor

    # Fill marking the available dates of the range, if using the `range` mode
    # or use `min_date/max_date`.
    canvas.before:
        Color:
            rgba:
                self.theme_cls.primaryColor[:-1] + [.3] \
                if self.is_in_range \
                else \
                self.theme_cls.transparentColor
        RoundedRectangle:
            size:
                (dp(42), dp(32)) \
                if self.is_range_end or self.is_week_end or self.is_month_end \
                else (dp(42), dp(32))
            pos: self.x, self.y + dp(6)
            radius:
                [
                dp(32) / 2 if self.is_range_start else 0,
                dp(32) / 2 if self.is_range_end else 0,
                dp(32) / 2 if self.is_range_end else 0,
                dp(32) / 2 if self.is_range_start else 0,
                ]

        # Selection circle.
        Color:
            rgba:
                self.theme_cls.primaryColor \
                if root.is_selected and not self.disabled \
                else self.theme_cls.transparentColor
        Ellipse:
            size: dp(42), dp(42)
            pos: self.pos
