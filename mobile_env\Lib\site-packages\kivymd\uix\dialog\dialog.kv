<MDDialog>
    radius: root.radius
    pos_hint: {"center_x": .5, "center_y": .5}
    size_hint: None, None
    height: container.height
    theme_shadow_color: "Custom"
    shadow_color: self.theme_cls.transparentColor
    focus_behavior: False

    RelativeLayout:
        size_hint_y: None
        height: container.height

        BoxLayout:
            id: container
            orientation: "vertical"
            size_hint_y: None
            height: self.minimum_height + button_container.height + dp(24)
            spacing: "16dp"
            padding: "24dp"
            y: button_container.height + dp(24)

            AnchorLayout:
                id: icon_container
                size_hint_y: None
                anchor_x: "center"
                height: self.children[0].height if self.children else 0

            BoxLayout:
                id: headline_container
                size_hint_y: None
                height: self.minimum_height

            BoxLayout:
                id: supporting_text_container
                size_hint_y: None
                height: self.minimum_height

            BoxLayout:
                id: content_container
                size_hint_y: None
                height: self.minimum_height

        BoxLayout:
            id: button_container
            size_hint_y: None
            height: self.minimum_height
            y: content_container.y


<MDDialogIcon>
    size_hint: None, None
    size: "24dp", "24dp"
    theme_font_size: "Custom"
    font_size: "24sp"
    icon_color:
        self.theme_cls.secondaryColor \
        if self.theme_icon_color == "Primary" else \
        ( \
        self.icon_color \
        if self.icon_color else self.theme_cls.transparentColor \
        )


<MDDialogHeadlineText>
    adaptive_height: True
    halign: "center"
    font_style: "Headline"
    role: "small"
    markup: True
    color:
        self.theme_cls.onSurfaceColor \
        if self.theme_text_color == "Primary" else ( \
        self.text_color \
        if self.text_color != self.theme_cls.transparentColor else \
        self.theme_cls.onSurfaceColor \
        )


<MDDialogSupportingText>
    adaptive_height: True
    halign: "center"
    font_style: "Body"
    role: "medium"
    markup: True
    text_color:
        self.theme_cls.onSurfaceVariantColor \
        if self.theme_text_color == "Primary" else ( \
        self.text_color \
        if self.text_color != self.theme_cls.transparentColor else \
        self.theme_cls.onSurfaceVariantColor \
        )


<MDDialogContentContainer>
    size_hint_y: None
    height: self.minimum_height


<MDDialogButtonContainer>
    size_hint_y: None
    height: self.minimum_height
    padding: "24dp", 0, "24dp", 0


<MDDialogScrim>
    canvas:
        Color:
            rgba: self.color[:-1] + [self.alpha]
        Rectangle:
            pos: self.pos
            size: self.size
