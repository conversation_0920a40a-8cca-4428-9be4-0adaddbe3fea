"""
UI清理工具模块

这个模块提供了一些工具函数，用于清理UI中的重复元素，
特别是重复的Logo实例，以提高应用的运行效率和用户体验。
"""
from kivy.clock import Clock
from kivy.app import App
from kivy.uix.screenmanager import ScreenManager

def cleanup_duplicate_logos(dt=None):
    """清理所有屏幕中的重复Logo

    这个函数会遍历应用中的所有屏幕，并清理每个屏幕中的重复Logo。

    Args:
        dt: Clock调度触发的时间增量（可选）

    Returns:
        int: 清理的重复Logo数量
    """
    from widgets.logo_manager import get_logo_manager
    from widgets.logo import HealthLogo
    logo_manager = get_logo_manager()

    # 获取应用实例
    app = App.get_running_app()
    if not app or not hasattr(app, 'root'):
        return 0

    # 获取屏幕管理器
    screen_manager = app.root
    if not isinstance(screen_manager, ScreenManager):
        return 0

    # 遍历所有屏幕，确保每个屏幕至少有一个Logo
    for screen_name in screen_manager.screen_names:
        screen = screen_manager.get_screen(screen_name)
        if screen:
            # 获取屏幕类名
            screen_class_name = screen.__class__.__name__

            # 检查当前屏幕中的Logo数量
            screen_logos = logo_manager._screen_logos.get(screen_class_name, [])

            # 如果当前屏幕中没有Logo，尝试找到并添加一个
            if not screen_logos:
                # 尝试在屏幕中找到HealthLogo组件
                for child in screen.walk():
                    if isinstance(child, HealthLogo):
                        # 已找到Logo，确保它被注册
                        logo_manager.register_logo(child)
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.debug(f"在屏幕 {screen_name} 中找到并注册了Logo")
                        break

    # 清理所有屏幕中的重复Logo
    total_cleaned = logo_manager.cleanup_duplicate_logos()

    return total_cleaned

def schedule_logo_cleanup(delay=5):
    """调度Logo清理任务

    这个函数会在指定的延迟后调度一个Logo清理任务。

    Args:
        delay: 延迟时间（秒）

    Returns:
        Event: Clock调度的事件
    """
    return Clock.schedule_once(cleanup_duplicate_logos, delay)

# 不再在模块加载时自动调度Logo清理任务，而是由主程序显式调用
# schedule_logo_cleanup(10)
