#:import Window kivy.core.window.Window


<MDBottomSheetDragHandle>
    orientation: "vertical"
    size_hint_y: None
    height: self.minimum_height
    padding: "16dp", "8dp", "16dp", "16dp"

    BottomSheetDragHandle:
        canvas:
            Color:
                rgba:
                    app.theme_cls.disabled_hint_text_color \
                    if not root.drag_handle_color else \
                    root.drag_handle_color
            SmoothRoundedRectangle:
                pos: self.pos
                size: self.size
                radius: [dp(4), ]

        size_hint: None, None
        size: "32dp", "4dp"
        pos_hint: {"center_x": .5}

    BottomSheetDragHandleContainer:
        id: header_container
        size_hint_y: None
        height: self.minimum_height


<MDBottomSheet>
    orientation: "vertical"
    radius: "16dp", "16dp", 0, 0
    padding: 0, "8dp", 0, 0
    -x: 0
    width: Window.width if Window.width <= dp(640) else dp(640)
    pos_hint: {"center_x": .5}
    y: self.height * (self.open_progress - 1)

    BoxLayout:
        id: drag_handle_container
        size_hint_y: None
        height: self.minimum_height
