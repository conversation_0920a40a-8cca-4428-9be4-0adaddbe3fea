# FocusBehavior 警告修复总结

## 问题描述

在使用 KivyMD 2.0.1.dev0 版本时，应用启动过程中会出现大量的 `FocusBehavior` 弃用警告：

```
[WARNING] [KivyMD] The `FocusBehavior` class is deprecated. It is recommended to use `StateFocusBehavior` instead of `FocusBehavior`.
```

这些警告来自 KivyMD 内部组件，而不是我们的项目代码。

## 问题根源

1. **KivyMD 2.0.1.dev0 版本变更**: 在 KivyMD 2.0 系列中，`FocusBehavior` 类被标记为弃用
2. **内部组件使用**: KivyMD 的许多内部组件仍在使用旧的 `FocusBehavior` 类
3. **警告频繁**: 每个使用 `FocusBehavior` 的组件都会产生警告，导致日志被大量警告信息污染

## 解决方案

### 1. 多层次警告过滤

在 `main.py` 中实现了三层警告过滤机制：

#### 第一层：Python warnings 模块过滤
```python
import warnings
def filter_kivymd_warnings(message, category, filename, lineno, file=None, line=None):
    """过滤KivyMD的FocusBehavior弃用警告"""
    message_str = str(message).lower()
    if ("focusbehavior" in message_str and 
        ("deprecated" in message_str or "recommend" in message_str) and 
        "statefocusbehavior" in message_str):
        return  # 忽略这个警告
    warnings.showwarning(message, category, filename, lineno, file, line)

warnings.showwarning = filter_kivymd_warnings
```

#### 第二层：Kivy Logger 过滤
```python
original_kivy_warning = KivyLogger.warning
def filtered_kivy_warning(msg):
    """过滤Kivy Logger中的FocusBehavior警告"""
    msg_str = str(msg).lower()
    if ("focusbehavior" in msg_str and 
        ("deprecated" in msg_str or "recommend" in msg_str) and 
        "statefocusbehavior" in msg_str):
        return  # 忽略这个警告
    original_kivy_warning(msg)

KivyLogger.warning = filtered_kivy_warning
```

#### 第三层：Python logging 模块过滤
```python
class FocusBehaviorFilter(logging.Filter):
    """过滤FocusBehavior弃用警告的日志过滤器"""
    def filter(self, record):
        message = record.getMessage().lower()
        if ("focusbehavior" in message and 
            ("deprecated" in message or "recommend" in message) and 
            "statefocusbehavior" in message):
            return False  # 不记录这个日志
        return True  # 记录其他日志

# 为所有相关的logger添加过滤器
focus_filter = FocusBehaviorFilter()
logging.getLogger('kivy').addFilter(focus_filter)
logging.getLogger('kivymd').addFilter(focus_filter)
logging.getLogger().addFilter(focus_filter)
```

### 2. 过滤器特点

- **智能匹配**: 使用小写字符串匹配，提高兼容性
- **精确过滤**: 只过滤 FocusBehavior 相关的弃用警告，保留其他重要警告
- **全面覆盖**: 覆盖所有可能的警告输出渠道
- **性能优化**: 过滤器逻辑简单高效，不影响应用性能

## 修复效果

### 修复前
```
[WARNING] [KivyMD] The `FocusBehavior` class is deprecated...
[WARNING] [KivyMD] The `FocusBehavior` class is deprecated...
[WARNING] [KivyMD] The `FocusBehavior` class is deprecated...
... (数十条重复警告)
```

### 修复后
```
2025-05-24 23:15:36,447 - __main__ - INFO - 检测到KivyMD版本: 2.0.1.dev0
[INFO   ] [检测到KivyMD版本 ] 2.0.1.dev0
2025-05-24 23:15:36,535 - __main__ - INFO - 检测到KivyMD 2.0系列版本，应用相应补丁
[INFO   ] 检测到KivyMD 2.0系列版本，应用相应补丁
... (清洁的日志输出，无FocusBehavior警告)
```

## 技术细节

### 过滤条件
过滤器检查以下条件（全部满足才过滤）：
1. 消息包含 "focusbehavior"
2. 消息包含 "deprecated" 或 "recommend"
3. 消息包含 "statefocusbehavior"

### 应用时机
- 在导入 Kivy/KivyMD 模块之前设置过滤器
- 确保在任何可能产生警告的代码执行前生效

## 注意事项

1. **保留其他警告**: 只过滤特定的 FocusBehavior 弃用警告，其他警告正常显示
2. **版本兼容性**: 适用于 KivyMD 2.0+ 版本
3. **性能影响**: 过滤器开销极小，不影响应用性能
4. **维护性**: 如果 KivyMD 更新警告消息格式，可能需要调整过滤条件

## 总结

通过实现多层次的警告过滤机制，成功解决了 KivyMD 2.0.1.dev0 中的 FocusBehavior 弃用警告问题。应用现在可以：

- ✅ 正常启动，无警告干扰
- ✅ 保留重要的警告信息
- ✅ 提供清洁的日志输出
- ✅ 维持良好的开发体验

这个解决方案是临时性的，当 KivyMD 官方完全迁移到 StateFocusBehavior 后，这些过滤器可以被移除。
