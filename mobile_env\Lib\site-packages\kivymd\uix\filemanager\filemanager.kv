#:import os os


<MDFileManagerItem>
    icon: "folder"
    path: ""
    background_normal: ""
    background_down: ""
    dir_or_file_name: ""
    icon_color: 0, 0, 0, 0
    _selected: False
    on_release: root.events_callback(root.path, root)

    MDListItemLeadingIcon:
        icon: root.icon
        theme_icon_color: "Custom"
        icon_color: root.icon_color

    MDListItemSupportingText:
        text: root.dir_or_file_name


<MDFileManagerItemPreview>
    name: ""
    path: ""
    realpath: ""
    type: "folder"
    events_callback: lambda x: None
    _selected: False
    orientation: "vertical"
    size_hint_y: None
    height: root.height
    padding: "20dp"
    spacing: "12dp"

    MDFileManagerThumbnail:
        mipmap: True
        source: root.path
        on_release:
            root.events_callback( \
            os.path.join(root.path if root.type != "folder" \
            else root.realpath, root.name), root)

    MDLabel:
        text: root.name
        adaptive_height: True
        shorten: True
        shorten_from: "center"
        halign: "center"
        text_size: self.width, None


<MDFileManager>
    canvas:
        Color:
            rgba: self.theme_cls.backgroundColor
        Rectangle:
            pos: self.pos
            size: self.size

    BoxLayout:
        orientation: "vertical"
        spacing: dp(5)

        MDTopAppBar:
            id: toolbar
            md_bg_color:
                app.theme_cls.surfaceColor \
                if not root.background_color_toolbar else \
                root.background_color_toolbar

            MDTopAppBarLeadingButtonContainer:
                padding: "12dp", 0, 0, 0

                MDActionTopAppBarButton:
                    icon: "chevron-left"
                    on_release: root.back()

            MDTopAppBarTitle:
                text: root.current_path

            MDTopAppBarTrailingButtonContainer:

                MDActionTopAppBarButton:
                    icon: "close-box"
                    on_release: root.exit_manager(1)

        RecycleView:
            id: rv
            key_viewclass: "viewclass"
            key_size: "height"
            bar_width: dp(4)
            bar_color: root.theme_cls.primaryColor

            RecycleGridLayout:
                padding: "10dp"
                spacing: "2dp"
                cols: 3 if root.preview else 1
                default_size: None, dp(48)
                default_size_hint: 1, None
                size_hint_y: None
                height: self.minimum_height
