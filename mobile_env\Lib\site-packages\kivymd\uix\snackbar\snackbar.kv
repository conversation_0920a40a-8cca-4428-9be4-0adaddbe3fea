<MDSnackbar>
    padding: 0, 0, "8dp", 0
    theme_bg_color: "Custom"
    theme_elevation_level: "Custom"
    theme_elevation_level: "Custom"
    style: "elevated"
    shadow_radius: self.radius
    elevation_level: 3
    size_hint_y: None
    height: self.minimum_height
    orientation: "vertical"
    md_bg_color:
        self.theme_cls.inverseSurfaceColor \
        if not self.background_color else \
        self.background_color

    BoxLayout:
        id: label_container
        padding: "16dp", "15dp", 0, "15dp"
        orientation: "vertical"
        size_hint_y: None
        height: self.minimum_height
        pos_hint: {"top": 1}
        spacing: "4dp"

    BoxLayout:
        id: button_container
        size_hint_x: None
        width: self.minimum_width


<MDSnackbarButtonContainer>
    size_hint_y: None
    height: self.minimum_height


<MDSnackbarText>
    adaptive_size: True
    font_style: "Label"
    role: "large"
    markup: True
    text_color:
        self.theme_cls.inversePrimaryColor \
        if self.theme_text_color == "Primary" else \
        self.text_color


<MDSnackbarSupportingText>
    adaptive_height: True
    font_style: "Body"
    role: "medium"
    markup: True
    text_color:
        self.theme_cls.inverseOnSurfaceColor \
        if self.theme_text_color == "Primary" else \
        self.text_color

<MDSnackbarActionButton>
    style: "text"
    pos_hint: {"right": 1}


<MDSnackbarCloseButton>
    text_color:
        self.theme_cls.inverseOnSurfaceColor \
        if self.theme_icon_color == "Primary" else \
        self.icon_color


<MDSnackbarActionButtonText>
    adaptive_size: True
    font_style: "Label"
    role: "large"
    markup: True
    color:
        self.theme_cls.inversePrimaryColor \
        if self.theme_text_color == "Primary" else \
        self.text_color
