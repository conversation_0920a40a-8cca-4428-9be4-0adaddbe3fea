#:import md_icons kivymd.icon_definitions.md_icons


<MDLabel>
    text_size:
        (self.width if not self.adaptive_width else None) \
        if not self.adaptive_size else None, \
        None
    color:
        self.text_color \
        if self.text_color else \
        self.theme_cls.onSurfaceColor
    disabled_color:
        app.theme_cls.onSurfaceColor[:-1] + \
        [self.label_opacity_value_disabled_text]
    font_size:
        self.theme_cls.font_styles[self.font_style][self.role]["font-size"] \
        if self.theme_font_size == "Primary" else self.font_size
    line_height:
        self.theme_cls.font_styles[self.font_style][self.role]["line-height"] \
        if self.theme_line_height == "Primary" else self.line_height
    font_name:
        self.theme_cls.font_styles[self.font_style][self.role]["font-name"] \
        if self.theme_font_name == "Primary" else self.font_name


<MDIcon>
    canvas:
        Color:
            rgba: (1, 1, 1, 1) if self.source else (0, 0, 0, 0)
        Rectangle:
            group: "rectangle"
            source: self.source
            pos:
                self.pos \
                if not self.source else \
                (self.x - (dp(18) / 2), self.y + dp(3))
            size:
                (0, 0) if not self.source else (dp(18), dp(18))

    font_style: "Icon"
    adaptive_size: True
    text:
        ( \
        u"{}".format(md_icons[self.icon]) \
        if self.icon in md_icons else \
        "blank" \
        ) \
        if self.font_name == "Icons" else self.icon
    color:
        self.icon_color \
        if self.icon_color else \
        self.theme_cls.onSurfaceVariantColor
