# widgets/camera_view.py
from kivy.uix.modalview import Mo<PERSON><PERSON>iew
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.image import Image
from kivy.clock import Clock
from kivy.graphics.texture import Texture
import cv2
from kivy.metrics import dp
from kivy.uix.label import Label
from theme import AppTheme
from api.api_client import APIClient
import os
import time
import json
import threading
import shutil
from typing import Optional, Dict, Any
import logging
from utils.user_manager import get_user_manager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CameraView(ModalView):
    def __init__(self, on_capture=None, on_dismiss=None, mode="id_card", callback=None, **kwargs):
        super(CameraView, self).__init__(**kwargs)
        self.size_hint = (0.9, 0.9)
        self.auto_dismiss = True
        # 支持新的on_capture参数，并兼容旧的callback参数
        self.on_capture_callback = on_capture or callback
        self.on_dismiss_callback = on_dismiss
        self.mode = mode  # 相机模式：id_card（身份证识别）或 document（文件拍照）
        self.api_client = APIClient()
        self.capture: Optional[cv2.VideoCapture] = None

        layout = BoxLayout(orientation='vertical', padding=dp(10), spacing=dp(10))

        # 相机组件
        self.camera = Image(size_hint=(1, 0.8))

        # 延迟初始化相机，提高响应速度
        Clock.schedule_once(self._init_camera, 0.1)

        # 模式提示标签
        mode_label = Label(
            text="身份证识别模式" if mode == "id_card" else "文件拍照模式",
            size_hint=(1, None),
            height=dp(30),
            color=AppTheme.PRIMARY_COLOR
        )

        # 按钮布局
        btn_layout = BoxLayout(
            orientation='horizontal',
            size_hint=(1, 0.2),
            spacing=dp(20),
            padding=[dp(10), dp(10), dp(10), dp(10)]
        )

        # 拍照按钮
        capture_btn = Button(
            text="拍照",
            size_hint=(0.5, None),
            height=dp(50),
            background_color=AppTheme.PRIMARY_COLOR
        )
        capture_btn.bind(on_release=self.take_photo)

        # 取消按钮
        cancel_btn = Button(
            text="取消",
            size_hint=(0.5, None),
            height=dp(50),
            background_color=AppTheme.ERROR_COLOR
        )
        cancel_btn.bind(on_release=self.on_cancel)

        # 添加组件到布局
        btn_layout.add_widget(capture_btn)
        btn_layout.add_widget(cancel_btn)

        layout.add_widget(mode_label)
        layout.add_widget(self.camera)
        layout.add_widget(btn_layout)

        self.add_widget(layout)

    def _init_camera(self, dt):
        """延迟初始化相机"""
        try:
            self.capture = cv2.VideoCapture(0)
            if self.capture is None:
                logger.error("Failed to initialize camera")
                return

            self.capture.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            # 使用更低的帧率，减少资源占用
            Clock.schedule_interval(self.update, 1.0/20.0)
            logger.info("Camera initialized successfully")
        except Exception as e:
            logger.error(f"Camera initialization failed: {str(e)}")

    def update(self, dt):
        """更新相机帧"""
        if not self.capture:
            return

        ret, frame = self.capture.read()
        if ret:
            # 将OpenCV图像转换为Kivy纹理
            buf = cv2.flip(frame, 0).tobytes()
            texture = Texture.create(size=(frame.shape[1], frame.shape[0]), colorfmt='bgr')
            texture.blit_buffer(buf, colorfmt='bgr', bufferfmt='ubyte')
            self.camera.texture = texture

    def take_photo(self, instance):
        """拍照并根据模式处理图像数据"""
        if not self.capture:
            logger.error("Camera not initialized")
            return

        ret, frame = self.capture.read()
        if ret:
            # 将OpenCV图像转换为Kivy图像
            buf = cv2.flip(frame, 0).tobytes()
            texture = Texture.create(size=(frame.shape[1], frame.shape[0]), colorfmt='bgr')
            texture.blit_buffer(buf, colorfmt='bgr', bufferfmt='ubyte')
            image = Image(texture=texture)

            if self.mode == "id_card":
                # 身份证识别模式：使用OCR识别身份证信息，不保存照片
                self._process_id_card(image)
            else:
                # 文件拍照模式：保存照片并上传至云端分析
                self._process_document(image)

            # 关闭相机视图
            self.dismiss()

    def on_cancel(self, instance):
        """取消拍照"""
        # 停止相机
        self.camera.play = False
        # 关闭相机视图
        self.dismiss()

    def dismiss(self, *args, **kwargs):
        """关闭相机视图并释放资源"""
        # 释放OpenCV相机
        if self.capture is not None:
            self.capture.release()

        # 调用dismiss回调
        if self.on_dismiss_callback:
            self.on_dismiss_callback()

        # 调用父类的dismiss方法
        super(CameraView, self).dismiss(*args, **kwargs)

    def _process_id_card(self, image):
        """处理身份证图像，提取信息并返回给调用者"""
        try:
            # 创建一个临时文件用于OCR处理
            temp_dir = "temp"
            if not os.path.exists(temp_dir):
                os.makedirs(temp_dir)

            temp_file = os.path.join(temp_dir, f"temp_id_{int(time.time())}.png")
            image.texture.save(temp_file)

            # 同时保存一份到项目根目录，方便查看最后一次拍摄的照片
            last_photo = "last_photo.png"
            image.texture.save(last_photo)
            logger.info(f"Saved last photo to {last_photo}")

            # 调用OCR API识别身份证信息
            result = self.api_client.ocr_id_card(temp_file)

            if result and "error" not in result:
                # 提取身份证信息
                id_info = {
                    "name": result.get("name", ""),
                    "id_number": result.get("id_number", ""),
                    "gender": result.get("gender", ""),
                    "birth_date": result.get("birth_date", ""),
                    "address": result.get("address", ""),
                    "ethnicity": result.get("ethnicity", "")
                }

                # 如果有回调函数，则将识别结果返回给调用者
                if self.on_capture_callback:
                    self.on_capture_callback(id_info)

                logger.info(f"Successfully processed ID card: {id_info}")
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"ID card processing failed: {error_msg}")
                if self.on_capture_callback:
                    self.on_capture_callback({"error": error_msg})

            # 删除临时文件，但保留last_photo.png
            if os.path.exists(temp_file):
                os.remove(temp_file)

        except Exception as e:
            error_msg = f"ID card processing failed: {str(e)}"
            logger.error(error_msg)
            if self.on_capture_callback:
                self.on_capture_callback({"error": error_msg})

    def _process_document(self, image):
        """处理文档图像，保存并上传至云端分析"""
        # 保存照片到本地
        photo_path = self.save_photo(image, directory="documents")

        # 创建一个线程来处理上传和分析，避免阻塞UI
        upload_thread = threading.Thread(target=self._upload_and_analyze_document, args=(photo_path,))
        upload_thread.daemon = True
        upload_thread.start()

        # 如果有回调函数，则返回照片路径
        if self.on_capture_callback:
            self.on_capture_callback({"photo_path": photo_path, "status": "processing"})

    def _upload_and_analyze_document(self, photo_path):
        """上传文档至云端并进行分析"""
        user = get_user_manager().get_current_user()
        if not user:
            print("未登录，无法上传")
            return
        try:
            # 上传文档至云端
            result = self.api_client.upload_document(photo_path, user_id=user.user_id, token=getattr(user, 'token', None))

            if result and "error" not in result:
                # 保存分析结果到本地数据库
                self._save_to_database(photo_path, result)

                # 创建本地备份
                backup_path = self._create_backup(photo_path)

                logger.info(f"Document processed successfully: {photo_path}")
                logger.info(f"Backup path: {backup_path}")
                logger.info(f"Analysis result: {result}")
            else:
                error_msg = result.get("error", "Unknown error")
                logger.error(f"Document processing failed: {error_msg}")

        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}")

    def _save_to_database(self, photo_path, analysis_result):
        """保存分析结果到本地数据库"""
        db_file = "data/documents_db.json"
        db_dir = os.path.dirname(db_file)

        # 确保目录存在
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)

        # 读取现有数据
        documents = []
        if os.path.exists(db_file):
            try:
                with open(db_file, 'r') as f:
                    documents = json.load(f)
            except:
                documents = []

        # 添加新记录
        documents.append({
            "photo_path": photo_path,
            "analysis_result": analysis_result,
            "timestamp": time.time()
        })

        # 保存数据
        with open(db_file, 'w') as f:
            json.dump(documents, f, indent=4)

    def _create_backup(self, photo_path):
        """创建本地备份"""
        # 创建备份目录
        backup_dir = "data/backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # 生成备份文件名
        filename = os.path.basename(photo_path)
        backup_path = os.path.join(backup_dir, filename)

        # 复制文件
        shutil.copy2(photo_path, backup_path)

        return backup_path

    def save_photo(self, image, directory="temp", filename=None):
        """保存照片到指定目录"""
        # 确保目录存在
        if not os.path.exists(directory):
            os.makedirs(directory)

        # 生成唯一文件名
        if filename is None:
            filename = f"photo_{int(time.time())}.png"

        filepath = os.path.join(directory, filename)

        # 保存图像
        image.texture.save(filepath)

        return filepath
