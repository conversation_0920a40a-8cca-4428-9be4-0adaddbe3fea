from kivy.app import App
from kivy.lang import Builder
from kivy.uix.widget import Widget
from kivy.animation import Animation
from kivy.properties import NumericProperty, StringProperty, ObjectProperty, DictProperty, BooleanProperty
from kivy.core.window import Window
from kivy.uix.image import Image
from kivy.uix.boxlayout import BoxLayout
from kivy.metrics import dp
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.label import MDLabel
from theme import FontStyles, FontManager

# 定义KV语言字符串
Builder.load_string('''
<HealthLogo>:
    orientation: 'vertical'
    size_hint: 1, None
    height: self.minimum_height
    spacing: dp(1)
    padding: [0, 0, 0, 0]
    pos_hint: {'center_x': 0.5}

    # Logo图片容器
    MDBoxLayout:
        id: logo_container
        orientation: 'vertical'
        size_hint: 1, None
        height: logo_image.height
        padding: [0, 0, 0, 0]
        pos_hint: {'center_x': 0.5}

        Image:
            id: logo_image
            source: root.logo_source
            size_hint: None, None
            size: root.logo_size
            pos_hint: {'center_x': 0.5}

    # 标题容器
    MDBoxLayout:
        id: title_container
        orientation: 'vertical'
        size_hint: 1, None
        height: title_label.height
        padding: [0, 0, 0, 0]
        pos_hint: {'center_x': 0.5}

        MDLabel:
            id: title_label
            text: root.title_text
            font_size: root.title_font_size
            font_name: root.title_font_name
            bold: root.title_bold
            halign: 'center'
            theme_text_color: "Custom"
            text_color: root.title_color
            size_hint: 1, None
            height: self.texture_size[1]
            padding: [dp(10), dp(2), dp(10), dp(2)]

    # 副标题容器
    MDBoxLayout:
        id: subtitle_container
        orientation: 'vertical'
        size_hint: 1, None
        height: subtitle_label.height
        padding: [0, 0, 0, 0]
        pos_hint: {'center_x': 0.5}

        MDLabel:
            id: subtitle_label
            text: root.subtitle_text
            font_size: root.subtitle_font_size
            font_name: root.subtitle_font_name
            bold: root.subtitle_bold
            halign: 'center'
            theme_text_color: "Custom"
            text_color: root.subtitle_color
            size_hint: 1, None
            height: self.texture_size[1]
            padding: [dp(10), dp(2), dp(10), dp(2)]
''')

class HealthLogo(MDBoxLayout):
    """统一的健康管理平台Logo组件

    这个组件提供了一个标准化的Logo显示，包括图片、标题和副标题，
    可以在应用的各个屏幕中保持一致的外观。
    """
    # 属性定义
    logo_source = StringProperty('assets/icons/health-Logo.png')
    title_text = StringProperty('伴君一生智能健康管理')
    subtitle_text = StringProperty('全生命周期健康管理平台')

    # 使用默认初始值，但会在初始化时更新
    title_font_size = NumericProperty(24)
    title_font_name = StringProperty(FontManager.get_font_name('bold'))
    title_bold = BooleanProperty(True)
    title_color = ObjectProperty((0.4, 0.4, 0.4, 1))

    subtitle_font_size = NumericProperty(12)
    subtitle_font_name = StringProperty(FontManager.get_font_name('regular'))
    subtitle_bold = BooleanProperty(False)
    subtitle_color = ObjectProperty((0.4, 0.4, 0.4, 1))

    # 尺寸属性
    logo_size = ObjectProperty((dp(60), dp(30)))

    # 类变量，用于跟踪已创建的实例数量
    _instances = 0

    def __init__(self, **kwargs):
        # 在初始化时更新字体样式属性，确保使用最新的theme.py设置
        self.update_font_styles()
        super(HealthLogo, self).__init__(**kwargs)
        self._update_sizes()

        # 增加实例计数，但不再发出警告，因为现在由LogoManager处理
        HealthLogo._instances += 1

        # 监听窗口大小变化
        Window.bind(on_resize=self._on_window_resize)

        # 在下一帧注册到LogoManager
        # 注意：这里不直接调用register_logo，因为在__init__时可能还没有添加到屏幕
        from kivy.clock import Clock
        Clock.schedule_once(self._register_to_manager, 0)

    def update_font_styles(self):
        """从theme.py更新字体样式设置"""
        # 重新导入以确保获取最新值
        import importlib
        import theme  # 导入整个模块
        importlib.reload(theme)  # 重新加载模块

        # 不再打印调试信息，避免重复输出
        # print(f"Theme LOGO_TITLE font_size: {theme.FontStyles.LOGO_TITLE['font_size']}")
        # print(f"Theme LOGO_SUBTITLE font_size: {theme.FontStyles.LOGO_SUBTITLE['font_size']}")

        # 更新标题字体样式
        self.title_font_size = theme.FontStyles.LOGO_TITLE['font_size']
        self.title_font_name = theme.FontStyles.LOGO_TITLE['font_name']
        self.title_bold = theme.FontStyles.LOGO_TITLE['bold']
        if 'color' in theme.FontStyles.LOGO_TITLE:
            self.title_color = theme.FontStyles.LOGO_TITLE['color']

        # 更新副标题字体样式
        self.subtitle_font_size = theme.FontStyles.LOGO_SUBTITLE['font_size']
        self.subtitle_font_name = theme.FontStyles.LOGO_SUBTITLE['font_name']
        self.subtitle_bold = theme.FontStyles.LOGO_SUBTITLE['bold']
        if 'color' in theme.FontStyles.LOGO_SUBTITLE:
            self.subtitle_color = theme.FontStyles.LOGO_SUBTITLE['color']

    def _update_sizes(self):
        """更新组件大小为固定尺寸"""
        # 使用固定尺寸和字体样式
        self.logo_size = (dp(170), dp(118))

    def _on_window_resize(self, instance, width, height):
        """窗口大小变化时不再需要调整组件尺寸，保留方法以避免错误"""
        pass

    def _register_to_manager(self, dt):
        """将自身注册到LogoManager

        这个方法会在组件添加到屏幕后的下一帧被调用，
        确保LogoManager能够正确识别Logo所属的屏幕。
        """
        try:
            # 导入LogoManager
            from widgets.logo_manager import get_logo_manager
            logo_manager = get_logo_manager()

            # 注册Logo
            logo_manager.register_logo(self)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"注册Logo到LogoManager时出错: {e}")

    def on_parent(self, instance, parent):
        """当Logo的父组件发生变化时调用

        这个方法会在Logo被添加到新的父组件时调用，
        确保LogoManager能够正确识别Logo所属的屏幕。
        """
        if parent:
            # 当添加到新的父组件时，重新注册到LogoManager
            from kivy.clock import Clock
            Clock.schedule_once(self._register_to_manager, 0)

# 辅助函数，用于在屏幕中添加Logo
def add_logo_to_layout(layout, **kwargs):
    """将Logo添加到指定布局中

    警告：
        不推荐在Python代码中使用此函数添加Logo。
        应该在KV语言中使用HealthLogo组件，以保持UI界面的一致性，
        并避免在同一界面中出现重复的Logo。

    Args:
        layout: 要添加Logo的布局
        **kwargs: 传递给HealthLogo的参数

    Returns:
        创建的HealthLogo实例或已存在的实例
    """
    # 检查布局中是否已经有Logo
    for child in layout.children:
        if isinstance(child, HealthLogo):
            print("\033[91m警告: 布局中已经存在Logo，不应重复添加\033[0m")
            print("\033[91m请在KV语言中使用HealthLogo组件，而不是在Python代码中添加\033[0m")
            # 返回已存在的Logo实例，不添加新的
            return child

    # 检查父布局中是否已经有Logo
    parent = layout.parent
    while parent:
        for child in parent.children:
            if isinstance(child, HealthLogo):
                print("\033[91m警告: 父布局中已经存在Logo，不应重复添加\033[0m")
                print("\033[91m请在KV语言中使用HealthLogo组件，而不是在Python代码中添加\033[0m")
                # 返回已存在的Logo实例，不添加新的
                return child
        parent = parent.parent

    # 如果没有存在的Logo，创建新的
    logo = HealthLogo(**kwargs)
    layout.add_widget(logo)
    return logo

# 测试代码
if __name__ == '__main__':
    class LogoApp(App):
        def build(self):
            return HealthLogo()

    LogoApp().run()