#:import DampedScrollEffect kivy.effects.dampedscroll.DampedScrollEffect


<MDTabsPrimary>
    orientation: "vertical"
    size_hint_y: None
    height: self.minimum_height

    MDTabsScrollView:
        id: tab_scroll
        do_scroll_x: False if container.width <= self.width else True

        canvas.before:
            Color:
                rgba:
                    root.md_bg_color \
                    if root.md_bg_color and root.theme_bg_color == "Custom" else \
                    root.theme_cls.surfaceColor
            Rectangle:
                pos: self.pos
                size: self.size

        GridLayout:
            id: container
            rows: 1
            size_hint: None, None
            width: self.minimum_width
            height: root.height

            canvas.before:
                Color:
                    rgba: root.theme_cls.primaryColor
                SmoothRoundedRectangle:
                    group: "md-tabs-rounded-rectangle"
                    pos:
                        self.x, \
                        self.y \
                        if not root._tabs_carousel else \
                        root._tabs_carousel.height
                    size: 0, root.indicator_height
                    radius: root.indicator_radius


<MDTabsItemIcon>
    pos_hint: {"center_x": .5}
    theme_icon_color: "Custom"
    icon_color: self.theme_cls.onSurfaceVariantColor


<MDTabsItemText>
    adaptive_size: True
    pos_hint: {"center_x": .5, "center_y": .5}
    padding: "36dp", 0, "36dp", 0
    font_style: "Title"
    role: "small"
    theme_text_color: "Custom"
    text_color: self.theme_cls.onSurfaceVariantColor


<MDTabsItem>
    orientation: "vertical"
    size_hint: None, None
    height: self.minimum_height
    spacing: "4dp"
    padding: 0, "12dp", 0, "8dp"


<MDTabsItemSecondaryContainer>


<MDTabsItemSecondary>
    size_hint: None, None
    height: "48dp"
    anchor_x: "center"
    anchor_y: "center"

    MDTabsItemSecondaryContainer:
        id: box_container
        size_hint: None, None
        size: self.minimum_size
        spacing: "8dp"


<MDTabsScrollView>
    size_hint: 1, None
    do_scroll_y: False
    bar_width: 0
    effect_cls: DampedScrollEffect


<MDTabsBadge>


<MDTabsCarousel>
